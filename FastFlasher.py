import os
import glob
import threading
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import serial.tools.list_ports
import subprocess
import struct
import sys
import multiprocessing
import runpy
import traceback
import random
import string
from datetime import datetime
import qrcode
from PIL import Image, ImageDraw, ImageFont
import re
import requests  # 添加requests库用于HTTP请求
import hashlib   # 添加hashlib库用于文件校验
import shutil    # 用于文件操作
import time  # 添加time库用于延迟

try:
    import pandas as pd
    import numpy as np
    PANDAS_AVAILABLE = True
    NUMPY_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    NUMPY_AVAILABLE = False


def resource_path(relative_path):
    """ Get absolute path to resource, works for dev and for PyInstaller """
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        # For --onedir mode, _MEIPASS is the directory of the executable
        base_path = sys._MEIPASS
    except Exception:
        # For development, base_path is the directory of the script
        # os.path.dirname(__file__) gives the directory of the current script
        base_path = os.path.abspath(os.path.dirname(__file__))
    return os.path.join(base_path, relative_path)


# Set IDF_PATH environment variable
try:
    idf_path_resolved = resource_path('esp-idf')
    os.environ['IDF_PATH'] = idf_path_resolved
    # print(f"DEBUG: IDF_PATH set to: {os.environ.get('IDF_PATH')}") # For verification
except Exception as e:
    # print(f"DEBUG: Warning: Could not set IDF_PATH. 'esp-idf' directory may be missing. Error: {e}")
    pass

CONFIG_FILE = resource_path("config.ini")
# SENSOR_TXT_FILE_NAME = resource_path("sensor_ids.txt") # Removed
# GATEWAY_TXT_FILE_NAME = resource_path("gateway_ids.txt") # Removed

# Define error signatures for sensor flashing process
SENSOR_ERROR_SIGNATURES = [
    "No response from device.",
    "Unable to open",
    # Add more specific error strings if needed
]

# --- ID Generation Utilities (from deviceIdsGeneration.py) ---
used_codes = set()

DEVICE_MODELS = {
    "网关": "A",
    "hyp60": "B",
    "hyp225": "C",
    "hypx": "D"
}


# Encode number to base-36 (A-Z0-9)
def to_base36(n):
    chars = string.ascii_uppercase + string.digits
    if n == 0:
        return chars[0]
    result = ''
    while n:
        result = chars[n % 36] + result
        n //= 36
    return result.rjust(3, '0')  # Pad to 3 chars


# Decode base-36 string to number
def from_base36(base36_str):
    chars = string.ascii_uppercase + string.digits
    value = 0
    for char_val in base36_str:  # Renamed char to char_val to avoid conflict if string.char exists
        value = value * 36 + chars.index(char_val)
    return value


# Generate unique 8-character code
def generate_unique_code(model_code: str, target_date: datetime = None):
    chars = string.ascii_uppercase + string.digits
    while True:
        # Get date components
        effective_date = target_date if target_date else datetime.now()
        year = effective_date.year - 2000  # 2025-2099 -> 25-99
        month = effective_date.month  # 1-12
        day = effective_date.day  # 1-31

        # Encode date: ((year * 12 + (month-1)) * 31 + (day-1))
        date_value = ((year * 12 + (month - 1)) * 31 + (day - 1))
        prefix = to_base36(date_value)  # 3 chars

        # Generate random 4-char suffix
        suffix = ''.join(random.choice(chars) for _ in range(4))
        code = prefix + model_code + suffix

        # Check uniqueness
        if code not in used_codes:
            used_codes.add(code)
            return code


def generate_device_ids_to_file(count: int, model_type_key: str, target_date: datetime = None):
    """
    Generates a specified number of unique device IDs and writes them to a file
    based on the model type.
    Returns a tuple (success: bool, message: str).
    """
    if count <= 0:
        return False, "错误: 生成的数量必须是正数。"

    if model_type_key not in DEVICE_MODELS:
        return False, f"错误: 无效的型号 '{model_type_key}'."

    model_code = DEVICE_MODELS[model_type_key]

    # Determine filename based on model_type_key for consistency
    # This part needs to align with how FastFlasher expects ID files, 
    # or be a distinct naming convention for generated files.
    # For now, using the logic from deviceIdsGeneration.py
    if model_type_key == "网关":
        filename = "gateway_ids.txt"  # This might conflict with user-selected files
    else:
        sensor_identifier = model_type_key.replace("hyp", "")
        filename = f"hyp{sensor_identifier}_ids.txt"  # This might conflict

    # It might be better to name them like "generated_gateway_ids.txt"
    # or save them in a specific sub-directory if these are meant to be
    # new files rather than overwriting existing ones used by the browse function.
    # For this integration, I will use the original naming from deviceIdsGeneration.py
    # but this is a point of attention.

    ids_to_write = []
    for _ in range(count):
        ids_to_write.append(generate_unique_code(model_code, target_date=target_date))

    try:
        # Ensure file is created in the application's resource path context if needed
        # For now, relative to CWD or script location if not bundled.
        # If files should go into app's data dir, use resource_path for the directory.
        output_dir = os.path.dirname(resource_path("dummy.txt"))  # Get base dir for resources
        full_file_path = os.path.join(output_dir, filename)

        with open(full_file_path, 'w') as f:
            for device_id in ids_to_write:
                f.write(f"{device_id}\n")
        return True, f"成功生成 {count} 个ID ({model_type_key}) 到 {full_file_path}"
    except IOError as e:
        return False, f"错误: 无法写入文件 {filename}. 错误: {e}"


# --- End ID Generation Utilities ---

# --- QR Code Utilities (includes encoding functions from user) ---
# Function to encode integer to base36 string (user provided for QR)
def qr_to_base36(num: int) -> str:
    chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    if num == 0:
        return "0"
    result = ""
    while num > 0:
        num, remainder = divmod(num, 36)
        result = chars[remainder] + result
    return result


# Function to decode base36 string to integer (user provided for QR)
def qr_from_base36(base36_str: str) -> int:
    chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    num = 0
    base36_str = base36_str.upper()
    for char_val in base36_str:
        num = num * 36 + chars.index(char_val)
    return num


# Function to encode a single ID with its type (user provided for QR)
def qr_encode_id_with_type(id_str: str, id_type: str) -> str:
    full_str = f"{id_type}:{id_str}"
    byte_data = full_str.encode('utf-8')
    int_data = int.from_bytes(byte_data, byteorder='big')
    base36_str = qr_to_base36(int_data)
    return f"DP:{base36_str}"


# Function to decode a full QR string (user provided for QR validation)
def qr_decode_full_str(qr_full_str: str) -> str:
    if not qr_full_str.upper().startswith("DP:"):
        raise ValueError("格式错误: 应以 'DP:' 开头")

    encoded_part = qr_full_str[3:]
    int_data = qr_from_base36(encoded_part)

    # Calculate required bytes.
    num_bytes = (int_data.bit_length() + 7) // 8

    byte_data = int_data.to_bytes(num_bytes, byteorder='big')
    try:
        decoded_str = byte_data.decode('utf-8')
    except UnicodeDecodeError:
        # Provide a hex representation if it's not valid UTF-8
        return f"无法解码为UTF-8文本: {byte_data.hex()}"

    return decoded_str


# --- End QR Code Utilities ---

# --- Filename Sanitization Utility ---
def sanitize_filename(filename_str):
    # Remove or replace characters illegal in Windows/Unix filenames
    if not isinstance(filename_str, str):  # Ensure it's a string
        filename_str = str(filename_str)

    # Replace slashes and backslashes first
    filename_str = filename_str.replace('/', '_').replace('\\', '_')

    # Define other illegal characters (Windows focus, generally good for cross-platform)
    illegal_chars_pattern = r'[<>:"|?*]'
    sane_name = re.sub(illegal_chars_pattern, "_", filename_str)

    # Remove control characters (ASCII 0-31)
    sane_name = "".join(c if ord(c) >= 32 else "_" for c in sane_name)

    # Remove leading/trailing whitespace and dots (Windows issues)
    sane_name = sane_name.strip(' .')
    if not sane_name:  # If filename becomes empty after sanitization
        sane_name = "_renamed_empty_"
    # Limit length if necessary - not implemented here for brevity but consider for production
    # max_len = 200 # Example
    # if len(sane_name) > max_len:
    #     name_part, ext_part = os.path.splitext(sane_name)
    #     sane_name = name_part[:max_len - len(ext_part) - (1 if ext_part else 0)] + ext_part
    return sane_name


# --- End Filename Sanitization Utility ---

class FastFlasherApp(tk.Tk):
    def __init__(self):
        super().__init__()
        
        # 密钥验证状态
        self.key_validated = False
        
        # S3固件配置
        self.FIRMWARE_S3_CONFIG = {
            "hyplink": {
                "url": "https://a7k3v9n2m5x6t4w0q8c1b9z6r2p3j0l7y5u1d8f.s3.eu-north-1.amazonaws.com/Q7b1Nf6Lk3Vx9Zr8Hj5Tg2Dp0S4w6M1Cq8Yv5Au",  # 设置固定网关固件URL
                "local_path": resource_path(os.path.join("hyplink", "hyplink.bin")),
                "backup_path": resource_path(os.path.join("hyplink", "hyplink.bin.bak")),
                "temp_path": resource_path(os.path.join("hyplink", "hyplink.bin.tmp"))
            },
            "hypsensor": {  # 添加传感器固件配置
                "url": "https://a7k3v9n2m5x6t4w0q8c1b9z6r2p3j0l7y5u1d8f.s3.eu-north-1.amazonaws.com/M2k9Tq6Xb4v7Cz1Gf0s8Hj5Lr3Pq7Wn2D0Kx4Rz",  # 设置固定传感器固件URL
                "local_path": resource_path(os.path.join("hypsensor", "HYP-60.bin")),  # 可能需要根据实际情况调整
                "backup_path": resource_path(os.path.join("hypsensor", "HYP-60.bin.bak")),
                "temp_path": resource_path(os.path.join("hypsensor", "HYP-60.bin.tmp"))
            }
        }
        
        # 设置窗口标题
        self.title("FastFlasher - Beima-Tech")
        self.geometry("600x650")
        self.resizable(True, True)

        self.sensor_id_var = tk.StringVar()
        self.sensor_ids_list = []
        self.sensor_current_id_index = -1
        self.sensor_last_successful_id = ""
        self.sensor_mcuboot_var = tk.StringVar()
        self.sensor_baud_var = tk.StringVar()
        self.sensor_port_var = tk.StringVar()
        self.sensor_id_file_path = tk.StringVar()  # Added
        self.sensor_jump_index_var = tk.StringVar()  # 添加序号变量
        self.sensor_broadcast_var = tk.BooleanVar()  # 添加广播版本勾选状态变量

        # 传感器标定相关变量
        self.sensor_calib_id_var = tk.StringVar()
        self.sensor_calib_ids_list = []
        self.sensor_calib_current_id_index = -1
        self.sensor_calib_last_successful_id = ""
        self.sensor_calib_mcuboot_var = tk.StringVar()
        self.sensor_calib_baud_var = tk.StringVar()
        self.sensor_calib_port_var = tk.StringVar()
        self.sensor_calib_id_file_path = tk.StringVar()
        self.sensor_calib_jump_index_var = tk.StringVar()
        self.sensor_calib_broadcast_var = tk.BooleanVar()
        self.sensor_calib_excel_file_path = tk.StringVar()  # 添加Excel文件路径变量

        self.gateway_id_var = tk.StringVar()
        self.gateway_ids_list = []
        self.gateway_current_id_index = -1
        self.gateway_last_successful_id = ""
        self.gateway_port_var = tk.StringVar()
        self.gateway_id_file_path = tk.StringVar()  # Added
        self.gateway_jump_index_var = tk.StringVar()  # 添加序号变量
        self.gateway_broadcast_var = tk.BooleanVar()  # 添加广播版本勾选状态变量

        self.logo_label = ttk.Label(self, text="Beima-Tech", font=("Helvetica", 18, "bold"))
        self.logo_label.pack(pady=10)

        self.notebook = ttk.Notebook(self)

        self.sensor_tab_frame = ttk.Frame(self.notebook, padding="10 10 10 10")
        self.sensor_calib_tab_frame = ttk.Frame(self.notebook, padding="10 10 10 10")
        self.gateway_tab_frame = ttk.Frame(self.notebook, padding="10 10 10 10")

        self.notebook.add(self.sensor_tab_frame, text='传感器 (Sensor)')
        self.notebook.add(self.sensor_calib_tab_frame, text='传感器标定')
        self.notebook.add(self.gateway_tab_frame, text='网关 (Gateway)')

        # Add ID Generation Tab
        self.id_gen_tab_frame = ttk.Frame(self.notebook, padding="10 10 10 10")
        self.notebook.add(self.id_gen_tab_frame, text='ID 生成')
        self._create_id_generation_tab()

        # Add QR Generation Tab
        self.qr_gen_tab_frame = ttk.Frame(self.notebook, padding="10 10 10 10")
        self.notebook.add(self.qr_gen_tab_frame, text='QR码生成')
        self._create_qr_gen_tab()
        
        # Add Firmware Update Tab - 在QR码生成标签页后面添加
        self.firmware_update_tab_frame = ttk.Frame(self.notebook, padding="10 10 10 10")
        self.notebook.add(self.firmware_update_tab_frame, text='固件更新')
        self._create_firmware_update_tab()

        self.notebook.pack(expand=True, fill='both', padx=10, pady=5)

        self._create_sensor_tab()
        self._create_sensor_calibration_tab()
        self._create_gateway_tab()

        self.scan_ports()
        self.load_config()

        # Check and create ID files if they don't exist
        # self._check_and_create_id_file(SENSOR_TXT_FILE_NAME, "传感器ID") # Removed/Commented
        # self._check_and_create_id_file(GATEWAY_TXT_FILE_NAME, "网关ID") # Removed/Commented

        # 传感器标签页不再有ID导航功能，跳过ID加载
        # self.load_and_set_initial_ids(
        #     self.sensor_ids_list,
        #     self.sensor_id_file_path,
        #     self.sensor_output_text,
        #     self.sensor_id_var,
        #     lambda index: setattr(self, 'sensor_current_id_index', index),
        #     lambda: self.sensor_current_id_index,
        #     lambda: self._update_id_navigation_buttons_state(
        #         self.sensor_ids_list, self.sensor_current_id_index,
        #         self.sensor_prev_id_btn, self.sensor_next_id_btn
        #     )
        # )

        self.load_and_set_initial_ids(
            self.sensor_calib_ids_list,
            self.sensor_calib_id_file_path,
            self.sensor_calib_output_text,
            self.sensor_calib_id_var,
            lambda index: setattr(self, 'sensor_calib_current_id_index', index),
            lambda: self.sensor_calib_current_id_index,
            lambda: self._update_id_navigation_buttons_state(
                self.sensor_calib_ids_list, self.sensor_calib_current_id_index,
                self.sensor_calib_prev_id_btn, self.sensor_calib_next_id_btn
            )
        )
        self.load_and_set_initial_ids(
            self.gateway_ids_list,
            # lambda: self._load_ids_from_txt(GATEWAY_TXT_FILE_NAME, self.gateway_ids_list, self.gateway_output_text), # Modified below
            self.gateway_id_file_path,
            self.gateway_output_text,
            self.gateway_id_var,
            lambda index: setattr(self, 'gateway_current_id_index', index),
            lambda: self.gateway_current_id_index,
            lambda: self._update_id_navigation_buttons_state(
                self.gateway_ids_list, self.gateway_current_id_index, self.gateway_prev_id_btn, self.gateway_next_id_btn
            )
        )

        # 创建菜单栏
        self.create_menu()

        # 如果密钥已验证，启动自动固件更新
        # if self.key_validated:
        #     self.after(1000, self.auto_update_all_firmware)

    def create_menu(self):
        menubar = tk.Menu(self)
        self.config(menu=menubar)


        

    def _create_sensor_tab(self):
        parent_tab_frame = self.sensor_tab_frame

        control_frame = ttk.Frame(parent_tab_frame)
        control_frame.pack(fill="x", pady=5, padx=5)

        ttk.Label(control_frame, text="串口选择:").grid(row=0, column=0, sticky="w")
        self.sensor_port_cb = ttk.Combobox(control_frame, textvariable=self.sensor_port_var, state="readonly", width=30)
        self.sensor_port_cb.grid(row=0, column=1, columnspan=2, sticky="ew", pady=2)
        self.sensor_port_cb.bind("<<ComboboxSelected>>", lambda e: self.save_config())
        self.sensor_port_cb.bind("<Button-1>", self._on_port_combobox_click)

        ttk.Label(control_frame, text="波特率:").grid(row=1, column=0, sticky="w")
        self.sensor_baud_cb = ttk.Combobox(control_frame, textvariable=self.sensor_baud_var, state="readonly",
                                           values=["9600", "115200", "230400"], width=30)
        self.sensor_baud_cb.grid(row=1, column=1, columnspan=2, sticky="ew", pady=2)
        self.sensor_baud_cb.bind("<<ComboboxSelected>>", lambda e: self.save_config())

        control_frame.columnconfigure(1, weight=1)

        # 添加广播版本勾选框
        self.sensor_broadcast_checkbox = ttk.Checkbutton(control_frame, text="使用广播版本",
                                                         variable=self.sensor_broadcast_var,
                                                         command=lambda: self.save_config())
        self.sensor_broadcast_checkbox.grid(row=2, column=0, columnspan=2, sticky="w", pady=2)

        # Frame for Text and Scrollbar (Sensor)
        sensor_output_frame = ttk.Frame(parent_tab_frame)
        sensor_output_frame.pack(fill="both", expand=True, pady=5, padx=5)

        self.sensor_scrollbar = ttk.Scrollbar(sensor_output_frame, orient=tk.VERTICAL)
        self.sensor_output_text = tk.Text(sensor_output_frame, height=15, state='disabled',
                                          bg="#f5f5f5", relief=tk.SUNKEN, borderwidth=1,
                                          yscrollcommand=self.sensor_scrollbar.set)
        self.sensor_scrollbar.config(command=self.sensor_output_text.yview)

        self.sensor_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.sensor_output_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Context menu for sensor output
        sensor_context_menu = self._create_clear_context_menu(self.sensor_output_text)
        self.sensor_output_text.bind("<Button-3>", lambda event: self._show_context_menu(event, sensor_context_menu))

        self.sensor_start_btn = ttk.Button(parent_tab_frame, text="开始传感器烧录", command=self.start_sensor_flashing)
        self.sensor_start_btn.pack(pady=10)

        self.sensor_status_label = ttk.Label(parent_tab_frame, text="", foreground="blue",
                                             anchor=tk.CENTER)  # Centered anchor
        self.sensor_status_label.pack(fill='x', padx=5)

        # 注意：传感器标签页已移除ID相关组件，因此不需要更新导航按钮状态
        # self._update_id_navigation_buttons_state(self.sensor_ids_list, self.sensor_current_id_index,
        #                                          self.sensor_prev_id_btn, self.sensor_next_id_btn)

    def _create_sensor_calibration_tab(self):
        parent_tab_frame = self.sensor_calib_tab_frame

        control_frame = ttk.Frame(parent_tab_frame)
        control_frame.pack(fill="x", pady=5, padx=5)

        ttk.Label(control_frame, text="串口选择:").grid(row=0, column=0, sticky="w")
        self.sensor_calib_port_cb = ttk.Combobox(control_frame, textvariable=self.sensor_calib_port_var, state="readonly", width=30)
        self.sensor_calib_port_cb.grid(row=0, column=1, columnspan=2, sticky="ew", pady=2)
        self.sensor_calib_port_cb.bind("<<ComboboxSelected>>", lambda e: self.save_config())
        self.sensor_calib_port_cb.bind("<Button-1>", self._on_port_combobox_click)

        ttk.Label(control_frame, text="波特率:").grid(row=1, column=0, sticky="w")
        self.sensor_calib_baud_cb = ttk.Combobox(control_frame, textvariable=self.sensor_calib_baud_var, state="readonly",
                                           values=["9600", "115200", "230400"], width=30)
        self.sensor_calib_baud_cb.grid(row=1, column=1, columnspan=2, sticky="ew", pady=2)
        self.sensor_calib_baud_cb.bind("<<ComboboxSelected>>", lambda e: self.save_config())

        ttk.Label(control_frame, text="MCUBoot工具:").grid(row=2, column=0, sticky="w")
        self.sensor_calib_mcuboot_cb = ttk.Combobox(control_frame, textvariable=self.sensor_calib_mcuboot_var, state="readonly",
                                              width=30)
        self.sensor_calib_mcuboot_cb.grid(row=2, column=1, columnspan=2, sticky="ew", pady=2)
        self.sensor_calib_mcuboot_cb.bind("<<ComboboxSelected>>", lambda e: self._on_mcuboot_selection_changed())
        self._scan_mcuboot_tools_for_calibration()

        ttk.Label(control_frame, text="传感器ID (8位):        ").grid(row=3, column=0, sticky="w", pady=2)

        self.sensor_calib_id_entry = ttk.Entry(control_frame, textvariable=self.sensor_calib_id_var, width=20)
        self.sensor_calib_id_entry.grid(row=3, column=1, sticky="ew", pady=2, padx=(0, 5))

        sensor_calib_button_cluster_frame = ttk.Frame(control_frame)
        sensor_calib_button_cluster_frame.grid(row=3, column=2, sticky="e", pady=2)

        self.sensor_calib_browse_id_btn = ttk.Button(sensor_calib_button_cluster_frame, text="...",
                                               command=lambda: self._browse_id_file('sensor_calib'), width=3)
        self.sensor_calib_browse_id_btn.pack(side=tk.LEFT, padx=(0, 2))

        self.sensor_calib_prev_id_btn = ttk.Button(sensor_calib_button_cluster_frame, text="<",
                                             command=lambda: self.show_previous_id(
                                                 self.sensor_calib_ids_list, self.sensor_calib_id_var,
                                                 lambda index: setattr(self, 'sensor_calib_current_id_index', index),
                                                 lambda: self.sensor_calib_current_id_index,
                                                 lambda: self._update_id_navigation_buttons_state(self.sensor_calib_ids_list,
                                                                                                  self.sensor_calib_current_id_index,
                                                                                                  self.sensor_calib_prev_id_btn,
                                                                                                  self.sensor_calib_next_id_btn)
                                             ), width=3)
        self.sensor_calib_prev_id_btn.pack(side=tk.LEFT, padx=(0, 2))
        self.sensor_calib_next_id_btn = ttk.Button(sensor_calib_button_cluster_frame, text=">", command=lambda: self.show_next_id(
            self.sensor_calib_ids_list, self.sensor_calib_id_var,
            lambda index: setattr(self, 'sensor_calib_current_id_index', index),
            lambda: self.sensor_calib_current_id_index,
            lambda: self._update_id_navigation_buttons_state(self.sensor_calib_ids_list, self.sensor_calib_current_id_index,
                                                             self.sensor_calib_prev_id_btn, self.sensor_calib_next_id_btn)
        ), width=3)
        self.sensor_calib_next_id_btn.pack(side=tk.LEFT)

        # 添加跳转到指定序号的功能
        ttk.Label(control_frame, text="序号:").grid(row=4, column=0, sticky="w", pady=2)

        # 创建一个框架来包含序号输入框和总数标签
        sensor_calib_jump_frame = ttk.Frame(control_frame)
        sensor_calib_jump_frame.grid(row=4, column=1, sticky="ew", pady=2)

        self.sensor_calib_jump_index_entry = ttk.Entry(sensor_calib_jump_frame, textvariable=self.sensor_calib_jump_index_var, width=8)
        self.sensor_calib_jump_index_entry.pack(side=tk.LEFT)

        # 绑定回车键和失去焦点事件
        self.sensor_calib_jump_index_entry.bind("<Return>", lambda e: self._jump_to_id_index(
            self.sensor_calib_ids_list,
            self.sensor_calib_id_var,
            self.sensor_calib_jump_index_var,
            lambda index: setattr(self, 'sensor_calib_current_id_index', index),
            lambda: self.sensor_calib_current_id_index,
            lambda: self._update_id_navigation_buttons_state(
                self.sensor_calib_ids_list,
                self.sensor_calib_current_id_index,
                self.sensor_calib_prev_id_btn,
                self.sensor_calib_next_id_btn
            ),
            self.sensor_calib_output_text
        ))

        self.sensor_calib_jump_index_entry.bind("<FocusOut>", lambda e: self._jump_to_id_index(
            self.sensor_calib_ids_list,
            self.sensor_calib_id_var,
            self.sensor_calib_jump_index_var,
            lambda index: setattr(self, 'sensor_calib_current_id_index', index),
            lambda: self.sensor_calib_current_id_index,
            lambda: self._update_id_navigation_buttons_state(
                self.sensor_calib_ids_list,
                self.sensor_calib_current_id_index,
                self.sensor_calib_prev_id_btn,
                self.sensor_calib_next_id_btn
            ),
            self.sensor_calib_output_text
        ))

        self.sensor_calib_total_count_label = ttk.Label(sensor_calib_jump_frame, text=f"/ {len(self.sensor_calib_ids_list)}")
        self.sensor_calib_total_count_label.pack(side=tk.LEFT, padx=(5, 0))

        control_frame.columnconfigure(1, weight=1)

        # 添加Excel文件选择（仅在选择225v3 mcuboot时显示）
        self.sensor_calib_excel_label = ttk.Label(control_frame, text="标定数据Excel:")
        self.sensor_calib_excel_label.grid(row=5, column=0, sticky="w", pady=2)

        self.sensor_calib_excel_frame = ttk.Frame(control_frame)
        self.sensor_calib_excel_frame.grid(row=5, column=1, columnspan=2, sticky="ew", pady=2)

        self.sensor_calib_excel_entry = ttk.Entry(self.sensor_calib_excel_frame, textvariable=self.sensor_calib_excel_file_path, width=25)
        self.sensor_calib_excel_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.sensor_calib_excel_browse_btn = ttk.Button(self.sensor_calib_excel_frame, text="浏览...",
                                                       command=self._browse_excel_file, width=8)
        self.sensor_calib_excel_browse_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # 添加广播版本勾选框
        self.sensor_calib_broadcast_checkbox = ttk.Checkbutton(control_frame, text="使用广播版本",
                                                         variable=self.sensor_calib_broadcast_var,
                                                         command=lambda: self.save_config())
        self.sensor_calib_broadcast_checkbox.grid(row=6, column=0, columnspan=2, sticky="w", pady=2)

        # Frame for Text and Scrollbar (Sensor Calibration)
        sensor_calib_output_frame = ttk.Frame(parent_tab_frame)
        sensor_calib_output_frame.pack(fill="both", expand=True, pady=5, padx=5)

        self.sensor_calib_scrollbar = ttk.Scrollbar(sensor_calib_output_frame, orient=tk.VERTICAL)
        self.sensor_calib_output_text = tk.Text(sensor_calib_output_frame, height=15, state='disabled',
                                          bg="#f5f5f5", relief=tk.SUNKEN, borderwidth=1,
                                          yscrollcommand=self.sensor_calib_scrollbar.set)
        self.sensor_calib_scrollbar.config(command=self.sensor_calib_output_text.yview)

        self.sensor_calib_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.sensor_calib_output_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Context menu for sensor calibration output
        sensor_calib_context_menu = self._create_clear_context_menu(self.sensor_calib_output_text)
        self.sensor_calib_output_text.bind("<Button-3>", lambda event: self._show_context_menu(event, sensor_calib_context_menu))

        self.sensor_calib_start_btn = ttk.Button(parent_tab_frame, text="开始传感器烧录", command=self.start_sensor_calibration_flashing)
        self.sensor_calib_start_btn.pack(pady=10)

        self.sensor_calib_status_label = ttk.Label(parent_tab_frame, text="", foreground="blue",
                                             anchor=tk.CENTER)  # Centered anchor
        self.sensor_calib_status_label.pack(fill='x', padx=5)

        self._update_id_navigation_buttons_state(self.sensor_calib_ids_list, self.sensor_calib_current_id_index,
                                                 self.sensor_calib_prev_id_btn, self.sensor_calib_next_id_btn)

        # 初始化Excel组件的可见性（在所有组件创建完成后调用）
        self.after(100, self._update_excel_components_visibility)

    def _create_gateway_tab(self):
        parent_tab_frame = self.gateway_tab_frame

        control_frame = ttk.Frame(parent_tab_frame)
        control_frame.pack(fill="x", pady=5, padx=5)

        ttk.Label(control_frame, text="网关COM端口:").grid(row=0, column=0, sticky="w")
        self.gateway_port_cb = ttk.Combobox(control_frame, textvariable=self.gateway_port_var, state="readonly",
                                            width=30)
        self.gateway_port_cb.grid(row=0, column=1, columnspan=2, sticky="ew", pady=2)
        self.gateway_port_cb.bind("<<ComboboxSelected>>", lambda e: self.save_config())
        self.gateway_port_cb.bind("<Button-1>", self._on_port_combobox_click)

        ttk.Label(control_frame, text="网关ID (8位):        ").grid(row=1, column=0, sticky="w", pady=2)

        self.gateway_id_entry = ttk.Entry(control_frame, textvariable=self.gateway_id_var, width=20)
        self.gateway_id_entry.grid(row=1, column=1, sticky="ew", pady=2, padx=(0, 5))

        gateway_button_cluster_frame = ttk.Frame(control_frame)
        gateway_button_cluster_frame.grid(row=1, column=2, sticky="e", pady=2)

        self.gateway_browse_id_btn = ttk.Button(gateway_button_cluster_frame, text="...",
                                                command=lambda: self._browse_id_file('gateway'), width=3)
        self.gateway_browse_id_btn.pack(side=tk.LEFT, padx=(0, 2))

        self.gateway_prev_id_btn = ttk.Button(gateway_button_cluster_frame, text="<",
                                              command=lambda: self.show_previous_id(
                                                  self.gateway_ids_list, self.gateway_id_var,
                                                  lambda index: setattr(self, 'gateway_current_id_index', index),
                                                  lambda: self.gateway_current_id_index,
                                                  lambda: self._update_id_navigation_buttons_state(
                                                      self.gateway_ids_list, self.gateway_current_id_index,
                                                      self.gateway_prev_id_btn, self.gateway_next_id_btn)
                                              ), width=3)
        self.gateway_prev_id_btn.pack(side=tk.LEFT, padx=(0, 2))
        self.gateway_next_id_btn = ttk.Button(gateway_button_cluster_frame, text=">", command=lambda: self.show_next_id(
            self.gateway_ids_list, self.gateway_id_var,
            lambda index: setattr(self, 'gateway_current_id_index', index),
            lambda: self.gateway_current_id_index,
            lambda: self._update_id_navigation_buttons_state(self.gateway_ids_list, self.gateway_current_id_index,
                                                             self.gateway_prev_id_btn, self.gateway_next_id_btn)
        ), width=3)
        self.gateway_next_id_btn.pack(side=tk.LEFT)

        # 添加跳转到指定序号的功能
        ttk.Label(control_frame, text="序号:").grid(row=2, column=0, sticky="w", pady=2)
        
        # 创建一个框架来包含序号输入框和总数标签
        gateway_jump_frame = ttk.Frame(control_frame)
        gateway_jump_frame.grid(row=2, column=1, sticky="ew", pady=2)
        
        self.gateway_jump_index_entry = ttk.Entry(gateway_jump_frame, textvariable=self.gateway_jump_index_var, width=8)
        self.gateway_jump_index_entry.pack(side=tk.LEFT)
        
        # 绑定回车键和失去焦点事件
        self.gateway_jump_index_entry.bind("<Return>", lambda e: self._jump_to_id_index(
            self.gateway_ids_list, 
            self.gateway_id_var,
            self.gateway_jump_index_var,
            lambda index: setattr(self, 'gateway_current_id_index', index),
            lambda: self.gateway_current_id_index,
            lambda: self._update_id_navigation_buttons_state(
                self.gateway_ids_list,
                self.gateway_current_id_index,
                self.gateway_prev_id_btn,
                self.gateway_next_id_btn
            ),
            self.gateway_output_text
        ))
        
        self.gateway_jump_index_entry.bind("<FocusOut>", lambda e: self._jump_to_id_index(
            self.gateway_ids_list, 
            self.gateway_id_var,
            self.gateway_jump_index_var,
            lambda index: setattr(self, 'gateway_current_id_index', index),
            lambda: self.gateway_current_id_index,
            lambda: self._update_id_navigation_buttons_state(
                self.gateway_ids_list,
                self.gateway_current_id_index,
                self.gateway_prev_id_btn,
                self.gateway_next_id_btn
            ),
            self.gateway_output_text
        ))
        
        self.gateway_total_count_label = ttk.Label(gateway_jump_frame, text=f"/ {len(self.gateway_ids_list)}")
        self.gateway_total_count_label.pack(side=tk.LEFT, padx=(5, 0))

        control_frame.columnconfigure(1, weight=1)
        
        # 添加广播版本勾选框
        # self.gateway_broadcast_checkbox = ttk.Checkbutton(control_frame, text="使用广播版本",
        #                                                  variable=self.gateway_broadcast_var,
        #                                                  command=lambda: self.save_config())
        # self.gateway_broadcast_checkbox.grid(row=3, column=0, columnspan=2, sticky="w", pady=2)

        # Frame for Text and Scrollbar (Gateway)
        gateway_output_frame = ttk.Frame(parent_tab_frame)
        gateway_output_frame.pack(fill="both", expand=True, pady=5, padx=5)

        self.gateway_scrollbar = ttk.Scrollbar(gateway_output_frame, orient=tk.VERTICAL)
        self.gateway_output_text = tk.Text(gateway_output_frame, height=15, state='disabled',
                                           bg="#f5f5f5", relief=tk.SUNKEN, borderwidth=1,
                                           yscrollcommand=self.gateway_scrollbar.set)
        self.gateway_scrollbar.config(command=self.gateway_output_text.yview)

        self.gateway_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.gateway_output_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Context menu for gateway output
        gateway_context_menu = self._create_clear_context_menu(self.gateway_output_text)
        self.gateway_output_text.bind("<Button-3>", lambda event: self._show_context_menu(event, gateway_context_menu))

        self.gateway_start_btn = ttk.Button(parent_tab_frame, text="开始网关烧录", command=self.start_gateway_flashing)
        self.gateway_start_btn.pack(pady=10)

        self.gateway_status_label = ttk.Label(parent_tab_frame, text="", foreground="darkgreen",
                                              anchor=tk.CENTER)  # Centered anchor
        self.gateway_status_label.pack(fill='x', padx=5)

        self._update_id_navigation_buttons_state(self.gateway_ids_list, self.gateway_current_id_index,
                                                 self.gateway_prev_id_btn, self.gateway_next_id_btn)

    def _on_port_combobox_click(self, event):
        self.scan_ports()

    def append_output(self, output_widget, text_line):
        output_widget.config(state='normal')
        output_widget.insert(tk.END, text_line)
        output_widget.see(tk.END)
        output_widget.config(state='disabled')

    def scan_ports(self):
        port_info_list = serial.tools.list_ports.comports()
        display_ports = []
        for port in port_info_list:
            # Format the string to include device and description
            display_ports.append(f"{port.device} ({port.description})")

        if hasattr(self, 'sensor_port_cb'):
            self.sensor_port_cb['values'] = display_ports
        if hasattr(self, 'sensor_calib_port_cb'):
            self.sensor_calib_port_cb['values'] = display_ports
        if hasattr(self, 'gateway_port_cb'):
            self.gateway_port_cb['values'] = display_ports

        # If a port was previously selected (from config), try to keep it.
        # The combobox might automatically keep the value if it exists in the new list.
        # If not, and if no value is set, default to the first available port.
        current_sensor_port = self.sensor_port_var.get()
        current_sensor_calib_port = self.sensor_calib_port_var.get()
        current_gateway_port = self.gateway_port_var.get()

        if display_ports:
            if hasattr(self, 'sensor_port_var') and not current_sensor_port:
                self.sensor_port_var.set(display_ports[0])
            elif current_sensor_port and current_sensor_port not in display_ports:
                # If saved port is gone, default to first
                self.sensor_port_var.set(display_ports[0])
                self.append_output(self.sensor_output_text,
                                   f"Info: 先前选择的传感器端口 '{current_sensor_port}' 不再可用。已选择 '{display_ports[0]}'。\n")

            if hasattr(self, 'sensor_calib_port_var') and not current_sensor_calib_port:
                self.sensor_calib_port_var.set(display_ports[0])
            elif current_sensor_calib_port and current_sensor_calib_port not in display_ports:
                # If saved port is gone, default to first
                self.sensor_calib_port_var.set(display_ports[0])
                self.append_output(self.sensor_calib_output_text,
                                   f"Info: 先前选择的传感器标定端口 '{current_sensor_calib_port}' 不再可用。已选择 '{display_ports[0]}'。\n")

            if hasattr(self, 'gateway_port_var') and not current_gateway_port:
                self.gateway_port_var.set(display_ports[0])
            elif current_gateway_port and current_gateway_port not in display_ports:
                self.gateway_port_var.set(display_ports[0])
                self.append_output(self.gateway_output_text,
                                   f"Info: 先前选择的网关端口 '{current_gateway_port}' 不再可用。已选择 '{display_ports[0]}'。\n")
        else:  # No ports found
            if hasattr(self, 'sensor_port_var'): self.sensor_port_var.set("")
            if hasattr(self, 'sensor_calib_port_var'): self.sensor_calib_port_var.set("")
            if hasattr(self, 'gateway_port_var'): self.gateway_port_var.set("")

    def _scan_mcuboot_tools(self):
        # Scan for mcuboot tools in hypsensor directory
        hypsensor_dir = resource_path("hypsensor")
        mcuboot_full_paths = glob.glob(os.path.join(hypsensor_dir, "*-mcuboot.exe"))
        if hasattr(self, 'sensor_mcuboot_cb'):
            if mcuboot_full_paths:
                display_names = [os.path.basename(p) for p in mcuboot_full_paths]
                self.sensor_mcuboot_cb['values'] = display_names

                current_selection = self.sensor_mcuboot_var.get()
                if not current_selection and display_names:
                    self.sensor_mcuboot_var.set(display_names[0])
                elif current_selection and current_selection not in display_names:
                    self.append_output(self.sensor_output_text,
                                       f"警告: 先前选择的MCUBoot工具 '{current_selection}' 未在hypsensor目录中找到。将默认选择第一个可用工具。\n")
                    if display_names:
                        self.sensor_mcuboot_var.set(display_names[0])
                    else:
                        self.sensor_mcuboot_var.set("")
            else:
                self.sensor_mcuboot_cb['values'] = []
                self.sensor_mcuboot_var.set("")
                messagebox.showerror("错误 - 传感器", "在 hypsensor 目录未找到mcuboot工具！",
                                     parent=self.sensor_tab_frame)

    def _scan_mcuboot_tools_for_calibration(self):
        # Scan for mcuboot tools in hypsensor directory for calibration tab
        hypsensor_dir = resource_path("hypsensor")
        mcuboot_full_paths = glob.glob(os.path.join(hypsensor_dir, "*-mcuboot.exe"))
        if hasattr(self, 'sensor_calib_mcuboot_cb'):
            if mcuboot_full_paths:
                display_names = [os.path.basename(p) for p in mcuboot_full_paths]
                self.sensor_calib_mcuboot_cb['values'] = display_names

                current_selection = self.sensor_calib_mcuboot_var.get()
                if not current_selection and display_names:
                    self.sensor_calib_mcuboot_var.set(display_names[0])
                elif current_selection and current_selection not in display_names:
                    self.append_output(self.sensor_calib_output_text,
                                       f"警告: 先前选择的MCUBoot工具 '{current_selection}' 未在hypsensor目录中找到。将默认选择第一个可用工具。\n")
                    if display_names:
                        self.sensor_calib_mcuboot_var.set(display_names[0])
                    else:
                        self.sensor_calib_mcuboot_var.set("")
            else:
                self.sensor_calib_mcuboot_cb['values'] = []
                self.sensor_calib_mcuboot_var.set("")
                messagebox.showerror("错误 - 传感器标定", "在 hypsensor 目录未找到mcuboot工具！",
                                     parent=self.sensor_calib_tab_frame)

    def _on_mcuboot_selection_changed(self):
        """当MCUBoot工具选择发生变化时的回调"""
        self.save_config()
        self._update_excel_components_visibility()

    def _update_excel_components_visibility(self):
        """根据选择的MCUBoot工具更新Excel组件的可见性"""
        mcuboot_tool = self.sensor_calib_mcuboot_var.get()
        is_225v3 = "225v3" in mcuboot_tool.lower() if mcuboot_tool else False

        if is_225v3:
            # 显示Excel相关组件
            self.sensor_calib_excel_label.grid()
            self.sensor_calib_excel_frame.grid()
            # 更新输出信息
            if hasattr(self, 'sensor_calib_output_text'):
                self.append_output(self.sensor_calib_output_text,
                                 f"Info: 已选择225v3 MCUBoot工具，需要提供标定数据Excel文件\n")
        else:
            # 隐藏Excel相关组件
            self.sensor_calib_excel_label.grid_remove()
            self.sensor_calib_excel_frame.grid_remove()
            # 清空Excel文件路径
            self.sensor_calib_excel_file_path.set("")
            # 更新输出信息
            if hasattr(self, 'sensor_calib_output_text'):
                self.append_output(self.sensor_calib_output_text,
                                 f"Info: 已选择非225v3 MCUBoot工具，将跳过标定数据处理步骤\n")

    def _browse_excel_file(self):
        """浏览选择Excel文件"""
        initial_dir = os.path.dirname(self.sensor_calib_excel_file_path.get()) if self.sensor_calib_excel_file_path.get() else "."

        file_path = filedialog.askopenfilename(
            title="选择传感器标定数据Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")],
            initialdir=initial_dir if os.path.isdir(initial_dir) else "."
        )

        if file_path:
            self.sensor_calib_excel_file_path.set(file_path)
            self.append_output(self.sensor_calib_output_text, f"Info: 已选择Excel文件: {file_path}\n")
            self.save_config()

    def _process_calibration_excel(self, excel_path, output_widget, sensor_id=None):
        """处理标定Excel文件，计算三个系数值并导出校正后的Excel"""
        try:
            # 检查pandas和numpy是否可用
            if not PANDAS_AVAILABLE:
                raise ImportError("需要安装pandas库来处理Excel文件")
            if not NUMPY_AVAILABLE:
                raise ImportError("需要安装numpy库来进行数值计算")

            # 读取Excel文件
            df = pd.read_excel(excel_path)
            # 清理列名中的空格
            df.columns = [c.strip() for c in df.columns]

            # 查找"读出电流"列名（兼容不同命名）
            ir_col = None
            for cand in ("读出电流A", "读out电流A", "readA"):
                if cand in df.columns:
                    ir_col = cand
                    break

            if ir_col is None:
                raise ValueError("未找到'读出电流'列，请检查Excel文件格式")

            uv_col = "实际微伏uV"
            ia_col = "实际电流A"

            # 检查必需的列是否存在
            required_cols = [uv_col, ia_col, ir_col]
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                raise ValueError(f"Excel文件缺少必需的列: {missing_cols}")

            self.append_output(output_widget, f">> 成功读取Excel文件，共{len(df)}行数据\n")
            self.append_output(output_widget, f">> 找到列: {list(df.columns)}\n")

            # 计算阈值 (uV_threshold)
            step = 5000  # 默认步长
            # 全量线性拟合 µV = k * I + b
            k, b = np.polyfit(df[ia_col], df[uv_col], 1)
            uv_at_20 = k * 20 + b
            # 向上取整到 step
            uv_threshold = int(np.ceil(uv_at_20 / step) * step)

            # 计算增益和偏移 (gain, offset)
            # 使用全部数据做线性回归
            gain, offset = np.polyfit(df[ir_col], df[ia_col], 1)

            self.append_output(output_widget, f">> 标定系数计算结果:\n")
            self.append_output(output_widget, f"   uV_threshold = {uv_threshold} µV\n")
            self.append_output(output_widget, f"   gain         = {gain:.7f}\n")
            self.append_output(output_widget, f"   offset       = {offset:.7f} A\n")

            # 导出校正后的Excel文件（和calc_sensor_coeff.py中的逻辑一样）
            if sensor_id:
                try:
                    # 校正并统计
                    df["校正电流A"] = np.where(df[uv_col] > uv_threshold,
                                           gain * df[ir_col] + offset,
                                           df[ir_col])
                    df["校正后绝对误差A"] = (df["校正电流A"] - df[ia_col]).abs()
                    df["校正后误差百分比"] = (df["校正后绝对误差A"] / df[ia_col] * 100).round(3)

                    # 创建CalibrationForm文件夹（如果不存在）
                    calibration_dir = "CalibrationForm"
                    if not os.path.exists(calibration_dir):
                        os.makedirs(calibration_dir)

                    # 生成输出文件名：传感器ID_calibrated.xlsx
                    output_filename = f"{sensor_id}_calibrated.xlsx"
                    output_path = os.path.join(calibration_dir, output_filename)

                    # 导出Excel文件
                    df.to_excel(output_path, index=False)

                    self.append_output(output_widget, f">> 校正后数据统计:\n")
                    self.append_output(output_widget, f"   平均绝对误差  = {df['校正后绝对误差A'].mean():.3f} A\n")
                    self.append_output(output_widget, f"   最大绝对误差  = {df['校正后绝对误差A'].max():.3f} A\n")
                    self.append_output(output_widget, f">> 校正后数据已导出到: {output_path}\n")

                except Exception as e:
                    self.append_output(output_widget, f"警告: 导出校正后Excel文件失败: {str(e)}\n")
                    # 不影响主流程，继续返回系数值

            return uv_threshold, gain, offset

        except Exception as e:
            self.append_output(output_widget, f"错误: 处理Excel文件失败: {str(e)}\n")
            raise

    def _load_ids_from_txt(self, txt_file_name, ids_list_ref, output_widget):  # Modified signature
        ids_list_ref.clear()
        current_id_index_val = -1
        try:
            if not txt_file_name or not os.path.exists(txt_file_name):  # Check if path is valid
                self.append_output(output_widget,
                                   f"Info: ID 文本文件 '{os.path.basename(txt_file_name) if txt_file_name else '<未选择>'}' 未找到或无效。\n")
                return False, current_id_index_val

            ids_loaded_count = 0
            with open(txt_file_name, 'r') as file:
                for line_idx, line in enumerate(file):
                    line = line.strip()
                    if line:
                        if len(line) == 8 and line.isalnum():
                            ids_list_ref.append(line.upper())
                            ids_loaded_count += 1
                        else:
                            self.append_output(output_widget,
                                               f"Info: 无效ID格式(8位字母数字)在文件 {os.path.basename(txt_file_name)} 行 {line_idx + 1}: '{line}'\n")

            if ids_loaded_count > 0:
                current_id_index_val = 0
                self.append_output(output_widget,
                                   f"Info: 从 {os.path.basename(txt_file_name)} 加载了 {ids_loaded_count} 个ID。\n")
                self._update_id_counts_display()  # 更新ID总数显示
                return True, current_id_index_val
            else:
                self.append_output(output_widget, f"Info: 未从 {os.path.basename(txt_file_name)} 加载有效ID。\n")
                self._update_id_counts_display()  # 更新ID总数显示
                return False, current_id_index_val
        except Exception as e:
            self.append_output(output_widget, f"Error: 读取文本文件 {os.path.basename(txt_file_name)} 失败: {e}\n")
            self._update_id_counts_display()  # 更新ID总数显示
            return False, current_id_index_val

    def load_and_set_initial_ids(self, ids_list_ref, id_file_path_var, output_widget, id_var, set_current_index_func,
                                 get_current_index_func, update_buttons_func):
        # Load IDs based on the path stored in id_file_path_var
        id_file_path = id_file_path_var.get()
        loaded_successfully = False
        current_id_index_val = -1

        if id_file_path and os.path.exists(id_file_path):
            loaded_successfully, current_id_index_val = self._load_ids_from_txt(id_file_path, ids_list_ref,
                                                                                output_widget)
        else:
            # If no path or file doesn't exist, clear the list and reset
            ids_list_ref.clear()
            self.append_output(output_widget, "Info: 未配置或找不到ID文件。请使用 '...' 按钮选择文件。\n")

        set_current_index_func(current_id_index_val if loaded_successfully and ids_list_ref else -1)

        # Try to restore the ID from config if it exists in the newly loaded list
        config_id = id_var.get().upper()  # Get ID potentially loaded from config

        if not loaded_successfully or not ids_list_ref:
            id_var.set("")  # Clear ID var if loading failed or list empty
            update_buttons_func()
            self._update_id_counts_display()  # 更新序号显示
            return

        # If an ID was loaded from config AND it's present in the *newly loaded* list, set the index
        if config_id and config_id in ids_list_ref:
            set_current_index_func(ids_list_ref.index(config_id))

        # Set the ID var based on the current index
        current_idx = get_current_index_func()
        if 0 <= current_idx < len(ids_list_ref):
            id_var.set(ids_list_ref[current_idx])
        elif ids_list_ref:  # If list is not empty but index is invalid (e.g., -1), default to first ID
            set_current_index_func(0)
            id_var.set(ids_list_ref[0])
        else:  # List is empty
            id_var.set("")

        update_buttons_func()
        self._update_id_counts_display()  # 更新序号显示

    def show_previous_id(self, ids_list_ref, id_var, set_current_index_func, get_current_index_func,
                         update_buttons_func):
        current_idx = get_current_index_func()
        if not ids_list_ref or current_idx <= 0:
            return
        set_current_index_func(current_idx - 1)
        id_var.set(ids_list_ref[get_current_index_func()])
        update_buttons_func()
        
        # 更新序号显示
        if hasattr(self, 'sensor_jump_index_var') and ids_list_ref is self.sensor_ids_list:
            self.sensor_jump_index_var.set(str(current_idx))
        elif hasattr(self, 'sensor_calib_jump_index_var') and ids_list_ref is self.sensor_calib_ids_list:
            self.sensor_calib_jump_index_var.set(str(current_idx))
        elif hasattr(self, 'gateway_jump_index_var') and ids_list_ref is self.gateway_ids_list:
            self.gateway_jump_index_var.set(str(current_idx))

    def show_next_id(self, ids_list_ref, id_var, set_current_index_func, get_current_index_func, update_buttons_func):
        current_idx = get_current_index_func()
        if not ids_list_ref or current_idx >= len(ids_list_ref) - 1:
            return
        set_current_index_func(current_idx + 1)
        id_var.set(ids_list_ref[get_current_index_func()])
        update_buttons_func()
        
        # 更新序号显示
        if hasattr(self, 'sensor_jump_index_var') and ids_list_ref is self.sensor_ids_list:
            self.sensor_jump_index_var.set(str(current_idx + 2))  # +2 因为当前索引已经+1，而显示从1开始
        elif hasattr(self, 'sensor_calib_jump_index_var') and ids_list_ref is self.sensor_calib_ids_list:
            self.sensor_calib_jump_index_var.set(str(current_idx + 2))  # +2 因为当前索引已经+1，而显示从1开始
        elif hasattr(self, 'gateway_jump_index_var') and ids_list_ref is self.gateway_ids_list:
            self.gateway_jump_index_var.set(str(current_idx + 2))  # +2 因为当前索引已经+1，而显示从1开始

    def _update_id_navigation_buttons_state(self, ids_list_ref, current_id_index, prev_btn, next_btn):
        if not ids_list_ref or len(ids_list_ref) <= 1:
            prev_btn.config(state=tk.DISABLED)
            next_btn.config(state=tk.DISABLED)
        else:
            prev_btn.config(state=tk.NORMAL if current_id_index > 0 else tk.DISABLED)
            next_btn.config(state=tk.NORMAL if current_id_index < len(ids_list_ref) - 1 else tk.DISABLED)

    def save_config(self):
        with open(CONFIG_FILE, "w") as f:
            # Sensor configs
            f.write(f"SENSOR_COM_PORT={self.sensor_port_var.get()}\n")
            f.write(f"SENSOR_BAUD_RATE={self.sensor_baud_var.get()}\n")
            f.write(f"SENSOR_MCUBOOT_TOOL={self.sensor_mcuboot_var.get()}\n")
            f.write(f"SENSOR_CURRENT_ID={self.sensor_id_var.get()}\n")
            f.write(f"SENSOR_LAST_SUCCESSFUL_ID={self.sensor_last_successful_id}\n")
            f.write(f"SENSOR_ID_FILE_PATH={self.sensor_id_file_path.get()}\n")  # Added
            f.write(f"SENSOR_BROADCAST={1 if self.sensor_broadcast_var.get() else 0}\n")  # 添加广播版本勾选状态
            # Sensor Calibration configs
            f.write(f"SENSOR_CALIB_COM_PORT={self.sensor_calib_port_var.get()}\n")
            f.write(f"SENSOR_CALIB_BAUD_RATE={self.sensor_calib_baud_var.get()}\n")
            f.write(f"SENSOR_CALIB_MCUBOOT_TOOL={self.sensor_calib_mcuboot_var.get()}\n")
            f.write(f"SENSOR_CALIB_CURRENT_ID={self.sensor_calib_id_var.get()}\n")
            f.write(f"SENSOR_CALIB_LAST_SUCCESSFUL_ID={self.sensor_calib_last_successful_id}\n")
            f.write(f"SENSOR_CALIB_ID_FILE_PATH={self.sensor_calib_id_file_path.get()}\n")
            f.write(f"SENSOR_CALIB_EXCEL_FILE_PATH={self.sensor_calib_excel_file_path.get()}\n")
            f.write(f"SENSOR_CALIB_BROADCAST={1 if self.sensor_calib_broadcast_var.get() else 0}\n")
            # Gateway configs
            f.write(f"GATEWAY_COM_PORT={self.gateway_port_var.get()}\n")
            f.write(f"GATEWAY_CURRENT_ID={self.gateway_id_var.get()}\n")
            f.write(f"GATEWAY_LAST_SUCCESSFUL_ID={self.gateway_last_successful_id}\n")
            f.write(f"GATEWAY_ID_FILE_PATH={self.gateway_id_file_path.get()}\n")  # Added
            f.write(f"GATEWAY_BROADCAST={1 if self.gateway_broadcast_var.get() else 0}\n")  # 添加广播版本勾选状态
            # 密钥验证状态
            f.write(f"KEY_VALIDATED={1 if self.key_validated else 0}\n")

        # After saving main config, update hypsensor/config.ini
        sensor_com_port_full = self.sensor_port_var.get()
        sensor_com_port_device = sensor_com_port_full.split(' ')[0] if sensor_com_port_full else ""
        self._update_hypsensor_config(
            sensor_com_port_device,
            self.sensor_baud_var.get(),
            self.sensor_mcuboot_var.get()  # This is already the display name
        )

    def load_config(self):
        self.sensor_port_var.set("")
        self.sensor_baud_var.set("115200")
        self.sensor_mcuboot_var.set("")
        self.sensor_id_var.set("")
        self.sensor_last_successful_id = ""
        self.sensor_id_file_path.set("")  # Added init
        self.sensor_broadcast_var.set(False)  # 广播版本默认不勾选

        # 传感器标定默认值
        self.sensor_calib_port_var.set("")
        self.sensor_calib_baud_var.set("115200")
        self.sensor_calib_mcuboot_var.set("")
        self.sensor_calib_id_var.set("")
        self.sensor_calib_last_successful_id = ""
        self.sensor_calib_id_file_path.set("")
        self.sensor_calib_excel_file_path.set("")
        self.sensor_calib_broadcast_var.set(False)

        self.gateway_port_var.set("")
        self.gateway_id_var.set("")
        self.gateway_last_successful_id = ""
        self.gateway_id_file_path.set("")  # Added init
        self.gateway_broadcast_var.set(False)  # 广播版本默认不勾选

        # 重置密钥验证状态
        self.key_validated = False

        if os.path.exists(CONFIG_FILE):
            config = {}
            with open(CONFIG_FILE, "r") as f:
                for line in f:
                    if "=" in line:
                        key, value = line.strip().split("=", 1)
                        config[key.strip()] = value.strip()

            self.sensor_port_var.set(config.get("SENSOR_COM_PORT", ""))
            self.sensor_baud_var.set(config.get("SENSOR_BAUD_RATE", "115200"))
            self.sensor_mcuboot_var.set(config.get("SENSOR_MCUBOOT_TOOL", ""))
            self.sensor_id_var.set(config.get("SENSOR_CURRENT_ID", ""))
            self.sensor_last_successful_id = config.get("SENSOR_LAST_SUCCESSFUL_ID", "")
            self.sensor_id_file_path.set(config.get("SENSOR_ID_FILE_PATH", ""))  # Added load
            self.sensor_broadcast_var.set(config.get("SENSOR_BROADCAST", "0") == "1")  # 加载广播版本勾选状态

            # 传感器标定配置加载
            self.sensor_calib_port_var.set(config.get("SENSOR_CALIB_COM_PORT", ""))
            self.sensor_calib_baud_var.set(config.get("SENSOR_CALIB_BAUD_RATE", "115200"))
            self.sensor_calib_mcuboot_var.set(config.get("SENSOR_CALIB_MCUBOOT_TOOL", ""))
            self.sensor_calib_id_var.set(config.get("SENSOR_CALIB_CURRENT_ID", ""))
            self.sensor_calib_last_successful_id = config.get("SENSOR_CALIB_LAST_SUCCESSFUL_ID", "")
            self.sensor_calib_id_file_path.set(config.get("SENSOR_CALIB_ID_FILE_PATH", ""))
            self.sensor_calib_excel_file_path.set(config.get("SENSOR_CALIB_EXCEL_FILE_PATH", ""))
            self.sensor_calib_broadcast_var.set(config.get("SENSOR_CALIB_BROADCAST", "0") == "1")

            self.gateway_port_var.set(config.get("GATEWAY_COM_PORT", ""))
            self.gateway_id_var.set(config.get("GATEWAY_CURRENT_ID", ""))
            self.gateway_last_successful_id = config.get("GATEWAY_LAST_SUCCESSFUL_ID", "")
            self.gateway_id_file_path.set(config.get("GATEWAY_ID_FILE_PATH", ""))  # Added load
            self.gateway_broadcast_var.set(config.get("GATEWAY_BROADCAST", "0") == "1")  # 加载广播版本勾选状态
            
            # 加载密钥验证状态
            self.key_validated = config.get("KEY_VALIDATED", "0") == "1"

    def start_sensor_flashing(self):
        threading.Thread(target=self._sensor_flash_process, daemon=True).start()

    def _sensor_flash_process(self):
        # Get the full descriptive string (e.g., "COM3 (USB-SERIAL CH340)")
        selected_port_string = self.sensor_port_var.get()
        if not selected_port_string:
            messagebox.showerror("错误 - 传感器", "请选择串口！", parent=self.sensor_tab_frame)
            return
        # Extract the actual port device name (e.g., "COM3")
        try:
            com_port = selected_port_string.split(' ')[0]
        except IndexError:
            messagebox.showerror("错误 - 传感器", f"无法从 '{selected_port_string}' 中解析端口号！",
                                 parent=self.sensor_tab_frame)
            return

        baud = self.sensor_baud_var.get()

        # 定义output_widget和其他UI元素变量，确保在使用前先初始化
        output_widget = self.sensor_output_text
        status_label_widget = self.sensor_status_label
        start_button_widget = self.sensor_start_btn

        # Define hypsensor directory using resource_path
        hypsensor_dir = resource_path("hypsensor")

        # 根据广播版本勾选状态选择固件路径
        if self.sensor_broadcast_var.get():
            # 使用广播版本固件 - 调用hyp60-bc.exe
            target_firmware = resource_path(os.path.join("hypsensor", "hyp60-bc.exe"))
            # 在输出窗口中显示使用广播版本信息
            self.append_output(output_widget, "\n>> 已选择广播版本固件 (hyp60-bc.exe) <<\n")
        else:
            # 使用普通版本固件路径
            target_firmware = resource_path(os.path.join("hypsensor", "hyp60.exe"))

        if not all([com_port, baud]):
            messagebox.showerror("错误 - 传感器", "请确保串口和波特率都已选择！",
                                 parent=self.sensor_tab_frame)
            return

        if not os.path.exists(target_firmware):
            messagebox.showerror("错误 - 传感器",
                                 f"缺少必要的文件！请确保 {os.path.basename(target_firmware)} 在 hypsensor 目录中！",
                                 parent=self.sensor_tab_frame)
            return

        status_label_widget.config(text="烧录中...")
        start_button_widget.config(state="disabled")

        try:
            # Calculate absolute path for the working directory
            abs_hypsensor_dir = resource_path("hypsensor")
            if not os.path.isdir(abs_hypsensor_dir):
                messagebox.showerror("错误 - 传感器", f"无法找到工作目录: {abs_hypsensor_dir}",
                                     parent=self.sensor_tab_frame)
                # Since this is a critical setup step, also disable button and set status
                start_button_widget.config(state="normal")  # Re-enable button before returning
                status_label_widget.config(text="错误: 工作目录丢失")
                return

            # 跳过mcuboot和ID写入步骤，直接执行传感器固件
            target_firmware_basename = os.path.basename(target_firmware)
            self.append_output(output_widget,
                               f"\n>> 开始执行传感器固件 {target_firmware} (工作目录: {abs_hypsensor_dir})\n")
            proc2 = subprocess.Popen([target_firmware],
                                     stdout=subprocess.PIPE,
                                     stderr=subprocess.PIPE,
                                     text=True,
                                     encoding='utf-8', errors='replace',
                                     creationflags=subprocess.CREATE_NO_WINDOW,
                                     cwd=abs_hypsensor_dir)

            stdout_data_2, stderr_data_2 = proc2.communicate()
            process_2_failed_by_signature = False
            first_detected_signature_proc2 = None  # Store the first specific signature found for proc2

            if stdout_data_2:
                self.append_output(output_widget, stdout_data_2)
                for signature in SENSOR_ERROR_SIGNATURES:
                    if signature in stdout_data_2:
                        self.append_output(output_widget, f"提示: 在标准输出中检测到特征 '{signature}' (hyp60)。")
                        if not first_detected_signature_proc2: first_detected_signature_proc2 = signature
                        process_2_failed_by_signature = True
            if stderr_data_2:
                self.append_output(output_widget, f"错误流输出 (hyp60):\n{stderr_data_2}")
                for signature in SENSOR_ERROR_SIGNATURES:
                    if signature in stderr_data_2:
                        self.append_output(output_widget, f"提示: 在错误流输出中检测到特征 '{signature}' (hyp60)。")
                        if not first_detected_signature_proc2: first_detected_signature_proc2 = signature
                        process_2_failed_by_signature = True

            # Handle specific user-friendly messages for proc2
            if first_detected_signature_proc2 == "Unable to open":
                messagebox.showerror("错误 - 传感器", "无法打开串口 (hyp60)。请检查串口是否选择正确或被其他程序占用。",
                                     parent=self.sensor_tab_frame)
                status_label_widget.config(text="串口打开失败 (hyp60)")
                return
            elif first_detected_signature_proc2 == "No response from device.":
                messagebox.showerror("错误 - 传感器",
                                     "设备无响应 (hyp60)。请检查传感器是否已正确连接，或是否需要手动进入Bootloader模式。",
                                     parent=self.sensor_tab_frame)
                status_label_widget.config(text="设备无响应 (hyp60)")
                return

            # General failure check for proc2
            if proc2.returncode != 0 or process_2_failed_by_signature:
                err_msg_detail = f"固件烧录工具 '{target_firmware}' 执行失败。"
                if process_2_failed_by_signature and not first_detected_signature_proc2:
                    err_msg_detail += " 检测到输出错误特征。"
                if proc2.returncode != 0:
                    err_msg_detail += f" 返回码: {proc2.returncode}."
                    # Raise error only if not already handled by a specific message box above
                    if not first_detected_signature_proc2 or first_detected_signature_proc2 not in ["Unable to open",
                                                                                                    "No response from device."]:
                        raise subprocess.CalledProcessError(
                            proc2.returncode if proc2.returncode != 0 else 1,
                            target_firmware,
                            output=stdout_data_2,
                            stderr=stderr_data_2
                        )

            status_label_widget.config(text="传感器烧录执行成功！")
            messagebox.showinfo("完成 - 传感器", "传感器烧录执行成功！", parent=self.sensor_tab_frame)

        except subprocess.CalledProcessError as e:
            status_label_widget.config(text="传感器烧录过程发生错误！")
            # Use e.cmd which should now hold the full path used in Popen
            cmd_str = ' '.join(e.cmd) if isinstance(e.cmd, list) else str(e.cmd)
            err_details = f"命令 '{cmd_str}' 执行失败，返回码 {e.returncode}."
            if e.stderr:
                err_details += f"\n错误输出:\n{e.stderr}"
            if e.stdout:
                self.append_output(output_widget, f"命令执行失败前的标准输出:\n{e.stdout}")
            self.append_output(output_widget, f"错误: {err_details}\n")
            messagebox.showerror("错误 - 传感器", err_details, parent=self.sensor_tab_frame)
        except FileNotFoundError as e:
            status_label_widget.config(text="找不到文件！")
            # Log the filename associated with the error if available
            fname = e.filename if hasattr(e, 'filename') else "(未知)"
            self.append_output(output_widget, f"错误: 尝试执行命令时找不到文件 '{fname}'\n错误详情: {str(e)}\n")
            messagebox.showerror("错误 - 传感器", f"找不到文件 '{fname}'！\n请检查路径和文件是否存在。\n{e}",
                                 parent=self.sensor_tab_frame)
        except Exception as e:
            status_label_widget.config(text="发生未知错误！")
            self.append_output(output_widget, f"未知错误: {str(e)}\n")
            self.append_output(output_widget, traceback.format_exc() + "\n")
            messagebox.showerror("错误 - 传感器", f"发生未知错误: {e}", parent=self.sensor_tab_frame)
        finally:
            start_button_widget.config(state="normal")

    def start_sensor_calibration_flashing(self):
        threading.Thread(target=self._sensor_calibration_flash_process, daemon=True).start()

    def _sensor_calibration_flash_process(self):
        # Get the full descriptive string (e.g., "COM3 (USB-SERIAL CH340)")
        selected_port_string = self.sensor_calib_port_var.get()
        if not selected_port_string:
            messagebox.showerror("错误 - 传感器标定", "请选择串口！", parent=self.sensor_calib_tab_frame)
            return
        # Extract the actual port device name (e.g., "COM3")
        try:
            com_port = selected_port_string.split(' ')[0]
        except IndexError:
            messagebox.showerror("错误 - 传感器标定", f"无法从 '{selected_port_string}' 中解析端口号！",
                                 parent=self.sensor_calib_tab_frame)
            return

        baud = self.sensor_calib_baud_var.get()
        mcuboot_display_name = self.sensor_calib_mcuboot_var.get()

        # 定义output_widget和其他UI元素变量，确保在使用前先初始化
        output_widget = self.sensor_calib_output_text
        status_label_widget = self.sensor_calib_status_label
        start_button_widget = self.sensor_calib_start_btn

        # Define hypsensor directory using resource_path
        hypsensor_dir = resource_path("hypsensor")

        # 根据广播版本勾选状态选择固件路径
        if self.sensor_calib_broadcast_var.get():
            # 使用广播版本固件 - 调用hyp60-bc.exe
            mcuboot_full_path = resource_path(
                os.path.join("hypsensor", mcuboot_display_name)) if mcuboot_display_name else ""
            target_firmware = resource_path(os.path.join("hypsensor", "hyp60-bc.exe"))
            # 在输出窗口中显示使用广播版本信息
            self.append_output(output_widget, "\n>> 已选择广播版本固件 (hyp60-bc.exe) <<\n")
        else:
            # 使用普通版本固件路径
            mcuboot_full_path = resource_path(
                os.path.join("hypsensor", mcuboot_display_name)) if mcuboot_display_name else ""
            target_firmware = resource_path(os.path.join("hypsensor", "hyp60.exe"))

        current_sensor_id = self.sensor_calib_id_var.get().upper()

        if not (len(current_sensor_id) == 8 and current_sensor_id.isalnum()):
            messagebox.showerror("错误 - 传感器标定", "传感器ID必须是8位有效的字母或数字字符！",
                                 parent=self.sensor_calib_tab_frame)
            return

        if self.sensor_calib_last_successful_id and current_sensor_id == self.sensor_calib_last_successful_id:
            proceed = messagebox.askyesno("确认操作 - 传感器标定",
                                          f"传感器ID '{current_sensor_id}' 已于上次成功烧录。\n\n"
                                          f"是否仍要继续烧录此ID？", parent=self.sensor_calib_tab_frame)
            if not proceed:
                status_label_widget.config(text=f"操作已取消。ID '{current_sensor_id}' 未重复烧录。")
                return

        if not all([com_port, baud, mcuboot_display_name]):
            messagebox.showerror("错误 - 传感器标定", "请确保串口、波特率和MCUBoot工具都已选择！",
                                 parent=self.sensor_calib_tab_frame)
            return

        # 检查是否选择了225v3 mcuboot工具
        is_225v3 = "225v3" in mcuboot_display_name.lower() if mcuboot_display_name else False

        # 只有选择225v3 mcuboot时才检查Excel文件
        excel_file_path = None
        if is_225v3:
            self.append_output(output_widget, f"\n>> 检测到225v3 MCUBoot工具，将进行标定数据处理\n")
            excel_file_path = self.sensor_calib_excel_file_path.get()
            if not excel_file_path:
                messagebox.showerror("错误 - 传感器标定", "选择225v3 MCUBoot工具时必须选择标定数据Excel文件！",
                                     parent=self.sensor_calib_tab_frame)
                return

            if not os.path.exists(excel_file_path):
                messagebox.showerror("错误 - 传感器标定", f"Excel文件不存在：{excel_file_path}",
                                     parent=self.sensor_calib_tab_frame)
                return
        else:
            self.append_output(output_widget, f"\n>> 检测到非225v3 MCUBoot工具，将跳过标定数据处理步骤\n")

        # 只有选择225v3 mcuboot时才预先验证Excel文件内容
        if is_225v3 and excel_file_path:
            try:
                self.append_output(output_widget, f">> 开始验证Excel文件: {excel_file_path}\n")
                # 检查pandas和numpy是否可用
                if not PANDAS_AVAILABLE:
                    raise ImportError("需要安装pandas库来处理Excel文件")
                if not NUMPY_AVAILABLE:
                    raise ImportError("需要安装numpy库来进行数值计算")

                # 读取Excel文件进行初步验证
                df = pd.read_excel(excel_file_path)
                df.columns = [c.strip() for c in df.columns]

                # 检查是否有数据
                if len(df) == 0:
                    raise ValueError("Excel文件中没有数据行")

                # 查找必需的列
                ir_col = None
                for cand in ("读出电流A", "读out电流A", "readA"):
                    if cand in df.columns:
                        ir_col = cand
                        break

                if ir_col is None:
                    raise ValueError("未找到'读出电流'列，请检查Excel文件格式")

                uv_col = "实际微伏uV"
                ia_col = "实际电流A"

                # 检查必需的列是否存在
                required_cols = [uv_col, ia_col, ir_col]
                missing_cols = [col for col in required_cols if col not in df.columns]
                if missing_cols:
                    raise ValueError(f"Excel文件缺少必需的列: {missing_cols}")

                # 检查数据是否为空或全为NaN
                for col in required_cols:
                    if df[col].isna().all():
                        raise ValueError(f"列'{col}'中没有有效数据")
                    if len(df[col].dropna()) == 0:
                        raise ValueError(f"列'{col}'中没有有效数据")

                self.append_output(output_widget, f">> Excel文件验证通过，共{len(df)}行有效数据\n")

            except Exception as e:
                messagebox.showerror("错误 - 传感器标定", f"Excel文件验证失败！\n错误: {e}",
                                     parent=self.sensor_calib_tab_frame)
                return
        elif is_225v3:
            # 如果是225v3但没有Excel文件，这种情况在前面已经处理了
            pass
        else:
            # 非225v3工具，跳过Excel验证
            self.append_output(output_widget, f">> 跳过Excel文件验证（非225v3 MCUBoot工具）\n")

        bin_file_display_name = mcuboot_display_name.replace('.exe', '.bin')

        # 根据广播版本勾选状态选择bin文件路径 - 保持与mcuboot工具在同一目录下
        bin_file_full_path = resource_path(os.path.join("hypsensor", bin_file_display_name))

        if not os.path.exists(bin_file_full_path):
            messagebox.showerror("错误 - 传感器标定", f"找不到对应的固件文件：{bin_file_full_path}",
                                 parent=self.sensor_calib_tab_frame)
            return

        if not os.path.exists(mcuboot_full_path) or not os.path.exists(target_firmware):
            messagebox.showerror("错误 - 传感器标定",
                                 f"缺少必要的文件！请确保 {os.path.basename(target_firmware)} 和 {mcuboot_display_name} 在 hypsensor 目录中！",
                                 parent=self.sensor_calib_tab_frame)
            return

        status_label_widget.config(text="烧录中...")
        start_button_widget.config(state="disabled")

        try:
            # Calculate absolute path for the working directory
            abs_hypsensor_dir = resource_path("hypsensor")
            if not os.path.isdir(abs_hypsensor_dir):
                messagebox.showerror("错误 - 传感器标定", f"无法找到工作目录: {abs_hypsensor_dir}",
                                     parent=self.sensor_calib_tab_frame)
                # Since this is a critical setup step, also disable button and set status
                start_button_widget.config(state="normal")  # Re-enable button before returning
                status_label_widget.config(text="错误: 工作目录丢失")
                return

            self.append_output(output_widget,
                               f"\n>> 准备写入传感器ID {current_sensor_id} (ASCII) 到 {bin_file_full_path} 的 0x3070 位置...\n")
            try:
                packed_id = current_sensor_id.encode('ascii')
                # 根据设备处理方式调整字节序
                # 设备使用小端序读取两个32位值，然后通过位移操作重新排列
                # 需要将每个4字节块内部反转，但保持块的顺序
                first_block = packed_id[0:4]
                second_block = packed_id[4:8]
                # 反转每个块内的字节顺序
                first_block_reversed = first_block[::-1]
                second_block_reversed = second_block[::-1]
                # 拼接反转后的块
                packed_id = first_block_reversed + second_block_reversed

                with open(bin_file_full_path, 'r+b') as f:
                    f.seek(0, 2)
                    file_size = f.tell()
                    if file_size < 0x3070 + 8:
                        messagebox.showerror("错误 - 传感器标定",
                                             f"文件 {bin_file_full_path} 太小！需要至少 {0x3070 + 8} 字节，但只有 {file_size} 字节",
                                             parent=self.sensor_calib_tab_frame)
                        return
                    f.seek(0x3070)
                    f.write(packed_id)
                    f.flush()
                    f.seek(0x3070)
                    written_data = f.read(8)
                    if written_data != packed_id:
                        messagebox.showerror("错误 - 传感器标定",
                                             f"写入传感器ID到 {bin_file_full_path} 验证失败！\n期望 (bytes): {packed_id.hex()}\n实际 (bytes): {written_data.hex()}",
                                             parent=self.sensor_calib_tab_frame)
                        return
                self.append_output(output_widget,
                                   f">> 传感器ID写入 {bin_file_full_path} 成功！ (ASCII: {current_sensor_id}, Bytes: {packed_id.hex()}, 适配设备处理方式)\n")
            except (IOError, ValueError, OSError) as e:
                status_label_widget.config(text="写入ID到BIN文件失败！")
                messagebox.showerror("错误 - 传感器标定", f"写入传感器ID到 {bin_file_full_path} 失败！\n错误: {e}",
                                     parent=self.sensor_calib_tab_frame)
                return

            # 只有选择225v3 mcuboot时才处理Excel文件并写入标定系数
            if is_225v3 and excel_file_path:
                self.append_output(output_widget, f"\n=== 开始225v3标定数据处理流程 ===\n")
                self.append_output(output_widget, f">> 处理标定数据Excel文件: {excel_file_path}\n")
                try:
                    # 处理Excel文件获取三个系数，并导出校正后的Excel
                    uv_threshold, gain, offset = self._process_calibration_excel(excel_file_path, output_widget, current_sensor_id)

                    # 将三个系数写入到0x3080位置
                    self.append_output(output_widget, f"\n>> 准备写入标定系数到 {bin_file_full_path} 的 0x3080 位置...\n")

                    # 将三个值打包为字节数据
                    # uv_threshold: uint32_t (4字节)
                    # gain: float (4字节)
                    # offset: float (4字节)
                    import struct
                    packed_coeffs = struct.pack('<Iff', int(uv_threshold), float(gain), float(offset))  # 小端序

                    with open(bin_file_full_path, 'r+b') as f:
                        f.seek(0, 2)
                        file_size = f.tell()
                        if file_size < 0x3080 + 12:  # 需要12字节空间
                            messagebox.showerror("错误 - 传感器标定",
                                                 f"文件 {bin_file_full_path} 太小！需要至少 {0x3080 + 12} 字节，但只有 {file_size} 字节",
                                                 parent=self.sensor_calib_tab_frame)
                            return
                        f.seek(0x3080)
                        f.write(packed_coeffs)
                        f.flush()
                        f.seek(0x3080)
                        written_data = f.read(12)
                        if written_data != packed_coeffs:
                            messagebox.showerror("错误 - 传感器标定",
                                                 f"写入标定系数到 {bin_file_full_path} 验证失败！\n期望 (bytes): {packed_coeffs.hex()}\n实际 (bytes): {written_data.hex()}",
                                                 parent=self.sensor_calib_tab_frame)
                            return

                    self.append_output(output_widget,
                                       f">> 标定系数写入 {bin_file_full_path} 成功！\n")
                    self.append_output(output_widget,
                                       f"   uV_threshold: {uv_threshold} (0x{struct.pack('<I', int(uv_threshold)).hex()})\n")
                    self.append_output(output_widget,
                                       f"   gain: {gain} (0x{struct.pack('<f', float(gain)).hex()})\n")
                    self.append_output(output_widget,
                                       f"   offset: {offset} (0x{struct.pack('<f', float(offset)).hex()})\n")
                    self.append_output(output_widget, f"=== 225v3标定数据处理完成 ===\n")

                except Exception as e:
                    status_label_widget.config(text="处理标定数据失败！")
                    messagebox.showerror("错误 - 传感器标定", f"处理标定数据失败！\n错误: {e}",
                                         parent=self.sensor_calib_tab_frame)
                    return
            else:
                self.append_output(output_widget, f"\n=== 跳过标定数据处理（非225v3 MCUBoot工具）===\n")

            self.append_output(output_widget, f"\n>> 开始执行 {mcuboot_full_path} (工作目录: {abs_hypsensor_dir})\n")
            # Execute mcuboot using full path AND absolute cwd
            proc1 = subprocess.Popen([mcuboot_full_path],  # Use full path in command list
                                     stdout=subprocess.PIPE,
                                     stderr=subprocess.PIPE,
                                     text=True,
                                     encoding='utf-8', errors='replace',
                                     creationflags=subprocess.CREATE_NO_WINDOW,
                                     cwd=abs_hypsensor_dir)  # Keep absolute path for cwd

            # Read stdout and stderr for mcuboot (proc1)
            stdout_data, stderr_data = proc1.communicate()
            process_failed_by_signature = False
            first_detected_signature_proc1 = None  # Store the first specific signature found

            if stdout_data:
                self.append_output(output_widget, stdout_data)
                for signature in SENSOR_ERROR_SIGNATURES:
                    if signature in stdout_data:
                        self.append_output(output_widget, f"提示: 在标准输出中检测到特征 '{signature}'。")
                        if not first_detected_signature_proc1: first_detected_signature_proc1 = signature
                        process_failed_by_signature = True
                        # Don't break here if you want to log all found signatures,
                        # but for specific error handling, the first one is often enough.
            if stderr_data:
                self.append_output(output_widget, f"错误流输出 (mcuboot):\n{stderr_data}")
                for signature in SENSOR_ERROR_SIGNATURES:
                    if signature in stderr_data:
                        self.append_output(output_widget, f"提示: 在错误流输出中检测到特征 '{signature}'。")
                        if not first_detected_signature_proc1: first_detected_signature_proc1 = signature
                        process_failed_by_signature = True

            # Handle specific user-friendly messages before general failure check
            if first_detected_signature_proc1 == "Unable to open":
                messagebox.showerror("错误 - 传感器标定", "无法打开串口。请检查串口是否选择正确或被其他程序占用。",
                                     parent=self.sensor_calib_tab_frame)
                status_label_widget.config(text="串口打开失败")
                # start_button_widget.config(state="normal") # Will be handled by finally
                return  # Abort flashing process
            elif first_detected_signature_proc1 == "No response from device.":
                messagebox.showerror("错误 - 传感器标定",
                                     "设备无响应。请检查传感器是否已正确连接，或是否需要手动进入Bootloader模式。",
                                     parent=self.sensor_calib_tab_frame)
                status_label_widget.config(text="设备无响应")
                # start_button_widget.config(state="normal") # Will be handled by finally
                return  # Abort flashing process

            # General failure check (return code or other signatures)
            if proc1.returncode != 0 or process_failed_by_signature:
                err_msg_detail = f"MCUBoot 工具 '{mcuboot_full_path}' 执行失败。"
                if process_failed_by_signature and not first_detected_signature_proc1:  # i.e. a signature from list, but not one of the special two
                    err_msg_detail += " 检测到输出错误特征。"
            if proc1.returncode != 0:
                err_msg_detail += f" 返回码: {proc1.returncode}."
                # Raise error only if not already handled by a specific message box above
                if not first_detected_signature_proc1 or first_detected_signature_proc1 not in ["Unable to open",
                                                                                                "No response from device."]:
                    raise subprocess.CalledProcessError(
                        proc1.returncode if proc1.returncode != 0 else 1,
                        mcuboot_full_path,
                        output=stdout_data,
                        stderr=stderr_data
                    )

            # Execute target firmware (proc2)
            target_firmware_basename = os.path.basename(target_firmware)
            self.append_output(output_widget,
                               f"\n>> MCUBoot执行成功，开始执行 {target_firmware} (工作目录: {abs_hypsensor_dir})\n")
            proc2 = subprocess.Popen([target_firmware],
                                     stdout=subprocess.PIPE,
                                     stderr=subprocess.PIPE,
                                     text=True,
                                     encoding='utf-8', errors='replace',
                                     creationflags=subprocess.CREATE_NO_WINDOW,
                                     cwd=abs_hypsensor_dir)

            stdout_data_2, stderr_data_2 = proc2.communicate()
            process_2_failed_by_signature = False
            first_detected_signature_proc2 = None  # Store the first specific signature found for proc2

            if stdout_data_2:
                self.append_output(output_widget, stdout_data_2)
                for signature in SENSOR_ERROR_SIGNATURES:
                    if signature in stdout_data_2:
                        self.append_output(output_widget, f"提示: 在标准输出中检测到特征 '{signature}' (hyp60)。")
                        if not first_detected_signature_proc2: first_detected_signature_proc2 = signature
                        process_2_failed_by_signature = True
            if stderr_data_2:
                self.append_output(output_widget, f"错误流输出 (hyp60):\n{stderr_data_2}")
                for signature in SENSOR_ERROR_SIGNATURES:
                    if signature in stderr_data_2:
                        self.append_output(output_widget, f"提示: 在错误流输出中检测到特征 '{signature}' (hyp60)。")
                        if not first_detected_signature_proc2: first_detected_signature_proc2 = signature
                        process_2_failed_by_signature = True

            # Handle specific user-friendly messages for proc2
            if first_detected_signature_proc2 == "Unable to open":
                messagebox.showerror("错误 - 传感器标定", "无法打开串口 (hyp60)。请检查串口是否选择正确或被其他程序占用。",
                                     parent=self.sensor_calib_tab_frame)
                status_label_widget.config(text="串口打开失败 (hyp60)")
                return
            elif first_detected_signature_proc2 == "No response from device.":
                messagebox.showerror("错误 - 传感器标定",
                                     "设备无响应 (hyp60)。请检查传感器是否已正确连接，或是否需要手动进入Bootloader模式。",
                                     parent=self.sensor_calib_tab_frame)
                status_label_widget.config(text="设备无响应 (hyp60)")
                return

            # General failure check for proc2
            if proc2.returncode != 0 or process_2_failed_by_signature:
                err_msg_detail = f"固件烧录工具 '{target_firmware}' 执行失败。"
                if process_2_failed_by_signature and not first_detected_signature_proc2:
                    err_msg_detail += " 检测到输出错误特征。"
                if proc2.returncode != 0:
                    err_msg_detail += f" 返回码: {proc2.returncode}."
                    # Raise error only if not already handled by a specific message box above
                    if not first_detected_signature_proc2 or first_detected_signature_proc2 not in ["Unable to open",
                                                                                                    "No response from device."]:
                        raise subprocess.CalledProcessError(
                            proc2.returncode if proc2.returncode != 0 else 1,
                            target_firmware,
                            output=stdout_data_2,
                            stderr=stderr_data_2
                        )

            status_label_widget.config(text="传感器标定烧录执行成功！")
            messagebox.showinfo("完成 - 传感器标定", "传感器标定烧录执行成功！", parent=self.sensor_calib_tab_frame)

            self.sensor_calib_last_successful_id = current_sensor_id
            self.save_config()

            if self.sensor_calib_ids_list and self.sensor_calib_current_id_index < len(self.sensor_calib_ids_list) - 1:
                self.show_next_id(self.sensor_calib_ids_list, self.sensor_calib_id_var,
                                  lambda i: setattr(self, 'sensor_calib_current_id_index', i),
                                  lambda: self.sensor_calib_current_id_index,
                                  lambda: self._update_id_navigation_buttons_state(self.sensor_calib_ids_list,
                                                                                   self.sensor_calib_current_id_index,
                                                                                   self.sensor_calib_prev_id_btn,
                                                                                   self.sensor_calib_next_id_btn))
                self.append_output(output_widget, f"Info: 自动跳转到下一个传感器ID: {self.sensor_calib_id_var.get()}\n")
            elif self.sensor_calib_ids_list and self.sensor_calib_current_id_index == len(self.sensor_calib_ids_list) - 1:
                self.append_output(output_widget, "Info: 已是传感器ID列表末尾。\n")

        except subprocess.CalledProcessError as e:
            status_label_widget.config(text="传感器标定烧录过程发生错误！")
            # Use e.cmd which should now hold the full path used in Popen
            cmd_str = ' '.join(e.cmd) if isinstance(e.cmd, list) else str(e.cmd)
            err_details = f"命令 '{cmd_str}' 执行失败，返回码 {e.returncode}."
            if e.stderr:
                err_details += f"\n错误输出:\n{e.stderr}"
            if e.stdout:
                self.append_output(output_widget, f"命令执行失败前的标准输出:\n{e.stdout}")
            self.append_output(output_widget, f"错误: {err_details}\n")
            messagebox.showerror("错误 - 传感器标定", err_details, parent=self.sensor_calib_tab_frame)
        except FileNotFoundError as e:
            status_label_widget.config(text="找不到文件！")
            # Log the filename associated with the error if available
            fname = e.filename if hasattr(e, 'filename') else "(未知)"
            self.append_output(output_widget, f"错误: 尝试执行命令时找不到文件 '{fname}'\n错误详情: {str(e)}\n")
            messagebox.showerror("错误 - 传感器标定", f"找不到文件 '{fname}'！\n请检查路径和文件是否存在。\n{e}",
                                 parent=self.sensor_calib_tab_frame)
        except Exception as e:
            status_label_widget.config(text="发生未知错误！")
            self.append_output(output_widget, f"未知错误: {str(e)}\n")
            self.append_output(output_widget, traceback.format_exc() + "\n")
            messagebox.showerror("错误 - 传感器标定", f"发生未知错误: {e}", parent=self.sensor_calib_tab_frame)
        finally:
            start_button_widget.config(state="normal")

    def start_gateway_flashing(self):
        threading.Thread(target=self._gateway_flash_process, daemon=True).start()

    def _gateway_flash_process(self):
        selected_gateway_port_string = self.gateway_port_var.get()
        current_gateway_id = self.gateway_id_var.get().upper()
        output_widget = self.gateway_output_text
        status_label_widget = self.gateway_status_label
        start_button_widget = self.gateway_start_btn

        if not selected_gateway_port_string:
            messagebox.showerror("错误 - 网关", "请选择网关COM端口！", parent=self.gateway_tab_frame)
            return
        try:
            com_port = selected_gateway_port_string.split(' ')[0]
        except IndexError:
            messagebox.showerror("错误 - 网关", f"无法从 '{selected_gateway_port_string}' 中解析端口号！",
                                 parent=self.gateway_tab_frame)
            return
        if not (len(current_gateway_id) == 8 and current_gateway_id.isalnum()):
            messagebox.showerror("错误 - 网关", "网关ID必须是8位有效的字母或数字字符！", parent=self.gateway_tab_frame)
            return
        if self.gateway_last_successful_id and current_gateway_id == self.gateway_last_successful_id:
            proceed = messagebox.askyesno("确认操作 - 网关",
                                          f"网关ID '{current_gateway_id}' 已于上次成功烧录。\n\n"
                                          f"是否仍要继续烧录此ID？", parent=self.gateway_tab_frame)
            if not proceed:
                status_label_widget.config(text=f"操作已取消。ID '{current_gateway_id}' 未重复烧录。")
                return

        status_label_widget.config(text="网关烧录中...")
        start_button_widget.config(state="disabled")

        cert_script_full_path = resource_path("configure_esp_secure_cert.py")
        ca_cert_path = resource_path(os.path.join("certificate", "AmazonRootCA1.pem"))
        device_cert_path = resource_path(os.path.join("certificate", "device_cert.pem.crt"))
        private_key_path = resource_path(os.path.join("certificate", "device_private.pem.key"))

        hyplink_dir = resource_path("hyplink")
        
        # 根据广播版本勾选状态选择固件路径
        if self.gateway_broadcast_var.get():
            # 使用广播版本固件 - 使用hyplink-bc.bin
            spiffs_file_path = resource_path(os.path.join("hyplink", "spiffs_storage.bin"))
            bootloader_bin = resource_path(os.path.join("hyplink", "bootloader.bin"))
            hyplink_bin_itself = resource_path(os.path.join("hyplink", "hyplink-bc.bin"))
            partition_table_bin = resource_path(os.path.join("hyplink", "partition-table.bin"))
            ota_data_initial_bin = resource_path(os.path.join("hyplink", "ota_data_initial.bin"))
            rcp_fw_bin = resource_path(os.path.join("hyplink", "rcp_fw.bin"))
            # 在输出窗口中显示使用广播版本信息
            self.append_output(output_widget, "\n>> 已选择网关广播版本固件 (hyplink-bc.bin) <<\n")
        else:
            # 使用普通版本固件路径
            spiffs_file_path = resource_path(os.path.join("hyplink", "spiffs_storage.bin"))
            bootloader_bin = resource_path(os.path.join("hyplink", "bootloader.bin"))
            hyplink_bin_itself = resource_path(os.path.join("hyplink", "hyplink.bin"))
            partition_table_bin = resource_path(os.path.join("hyplink", "partition-table.bin"))
            ota_data_initial_bin = resource_path(os.path.join("hyplink", "ota_data_initial.bin"))
            rcp_fw_bin = resource_path(os.path.join("hyplink", "rcp_fw.bin"))

        required_files = [
            ca_cert_path, device_cert_path, private_key_path,
            spiffs_file_path, bootloader_bin, hyplink_bin_itself, partition_table_bin,
            ota_data_initial_bin, rcp_fw_bin,
            cert_script_full_path
        ]
        for f_path in required_files:
            if not os.path.exists(f_path):
                messagebox.showerror("错误 - 网关", f"关键文件或工具未找到: {f_path}", parent=self.gateway_tab_frame)
                status_label_widget.config(text="文件缺失，操作中止。")
                start_button_widget.config(state="normal")
                return

        try:
            # Step 1: Burn Certificate (remains Popen with sys.executable + script_path)
            self.append_output(output_widget, "\n>> 步骤1: 开始烧录证书...\n")
            popen_cwd_cert = resource_path("")
            self.append_output(output_widget, f"DEBUG: Setting Popen CWD for cert script to: {popen_cwd_cert}\n")
            cmd_cert = [
                sys.executable, cert_script_full_path, "-p", com_port,
                "--keep_ds_data_on_host", "--ca-cert", ca_cert_path,
                "--device-cert", device_cert_path, "--private-key", private_key_path,
                "--target_chip", "esp32s3", "--secure_cert_type", "cust_flash",
                "--priv_key_algo", "RSA", "2048"
            ]
            self.append_output(output_widget, f"Executing (in {popen_cwd_cert}): {' '.join(cmd_cert)}\n")
            proc_cert = subprocess.Popen(cmd_cert, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                         text=True, encoding='utf-8', errors='replace',
                                         creationflags=subprocess.CREATE_NO_WINDOW, cwd=popen_cwd_cert)

            # 使用新的辅助方法实时读取并显示输出
            returncode = self._read_process_output(proc_cert, output_widget)

            if returncode != 0:
                self.append_output(output_widget, f"错误: 证书烧录失败，返回码 {returncode}\n")
                raise subprocess.CalledProcessError(returncode, cmd_cert)
            self.append_output(output_widget, ">> 步骤1: 证书烧录成功。\n")

            # Step 2: Modify spiffs_storage.bin (remains the same)
            self.append_output(output_widget,
                               f"\n>> 步骤2: 开始修改 {spiffs_file_path} 写入网关ID {current_gateway_id}...\n")
            try:
                packed_gateway_id = current_gateway_id.encode('ascii')
                # 根据设备处理方式调整字节序
                # 设备使用小端序读取两个32位值，然后通过位移操作重新排列
                # 需要将每个4字节块内部反转，但保持块的顺序
                # first_block = packed_gateway_id[0:4]
                # second_block = packed_gateway_id[4:8]
                # # 反转每个块内的字节顺序
                # first_block_reversed = first_block[::-1]
                # second_block_reversed = second_block[::-1]
                # # 拼接反转后的块
                # packed_gateway_id = first_block_reversed + second_block_reversed
                
                with open(spiffs_file_path, 'r+b') as f:
                    f.seek(0, 2);
                    file_size = f.tell()
                    if file_size < 0x1B08 + 8:
                        messagebox.showerror("错误 - 网关",
                                             f"文件 {spiffs_file_path} 太小！需要至少 {0x1B08 + 8} 字节，但只有 {file_size} 字节",
                                             parent=self.gateway_tab_frame)
                        raise IOError(f"{spiffs_file_path} too small for ID write.")
                    f.seek(0x1B08);
                    f.write(packed_gateway_id);
                    f.flush();
                    f.seek(0x1B08)
                    written_data = f.read(8)
                    if written_data != packed_gateway_id:
                        messagebox.showerror("错误 - 网关", f"写入网关ID到 {spiffs_file_path} 验证失败！",
                                             parent=self.gateway_tab_frame)
                        raise IOError(f"Verification failed for ID write in {spiffs_file_path}")
                self.append_output(output_widget,
                                   f">> 步骤2: 网关ID写入 {spiffs_file_path} 成功。 (ASCII: {current_gateway_id}, Bytes: {packed_gateway_id.hex()})\n")
            except (IOError, ValueError, OSError) as e:
                status_label_widget.config(text=f"修改 {os.path.basename(spiffs_file_path)} 失败！")
                messagebox.showerror("错误 - 网关", f"修改 {spiffs_file_path} 写入网关ID失败！\n错误: {e}",
                                     parent=self.gateway_tab_frame)
                raise

            # Step 3: Burn Firmware (REVERT to Popen with sys.executable -m esptool)
            self.append_output(output_widget, f"\n>> 步骤3: 开始烧录网关固件 (使用 sys.executable -m esptool)...\n")
            cmd_firmware = [
                sys.executable, "-m", "esptool", "-p", com_port, "-b", "460800",
                "--before", "default_reset", "--after", "hard_reset", "--chip", "esp32s3",
                "--no-stub", "write_flash", "--flash_mode", "dio", "--flash_freq", "80m",
                "--flash_size", "8MB",
                "0x0", bootloader_bin, "0x30000", hyplink_bin_itself, "0xb000", partition_table_bin,
                "0x25000", ota_data_initial_bin, "0x521000", rcp_fw_bin, "0x5c1000", spiffs_file_path
            ]
            self.append_output(output_widget, f"Executing: {' '.join(cmd_firmware)}\n")
            popen_cwd_esptool = resource_path("")  # _internal/ directory
            proc_firmware = subprocess.Popen(cmd_firmware, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                             text=True, encoding='utf-8', errors='replace',
                                             creationflags=subprocess.CREATE_NO_WINDOW, cwd=popen_cwd_esptool)

            # 使用新的辅助方法实时读取并显示输出
            returncode = self._read_process_output(proc_firmware, output_widget)

            if returncode != 0:
                self.append_output(output_widget, f"错误: 网关固件烧录失败，返回码 {returncode}\n")
                raise subprocess.CalledProcessError(returncode, cmd_firmware)
            self.append_output(output_widget, ">> 步骤3: 网关固件烧录成功。\n")

            status_label_widget.config(text="网关烧录全部步骤成功！")
            messagebox.showinfo("完成 - 网关", "网关烧录全部步骤成功！", parent=self.gateway_tab_frame)
            self.gateway_last_successful_id = current_gateway_id
            self.save_config()
            if self.gateway_ids_list and self.gateway_current_id_index < len(self.gateway_ids_list) - 1:
                self.show_next_id(self.gateway_ids_list, self.gateway_id_var,
                                  lambda i: setattr(self, 'gateway_current_id_index', i),
                                  lambda: self.gateway_current_id_index,
                                  lambda: self._update_id_navigation_buttons_state(self.gateway_ids_list,
                                                                                   self.gateway_current_id_index,
                                                                                   self.gateway_prev_id_btn,
                                                                                   self.gateway_next_id_btn))
                self.append_output(output_widget, f"Info: 自动跳转到下一个网关ID: {self.gateway_id_var.get()}\n")
            elif self.gateway_ids_list and self.gateway_current_id_index == len(self.gateway_ids_list) - 1:
                self.append_output(output_widget, "Info: 已是网关ID列表末尾。\n")

        except subprocess.CalledProcessError as e:
            status_label_widget.config(text="网关烧录过程发生错误！")
            cmd_str = ' '.join(e.cmd) if isinstance(e.cmd, list) else str(e.cmd)
            self.append_output(output_widget, f"错误: 命令 '{cmd_str}' 执行失败。\n")
            messagebox.showerror("错误 - 网关", f"命令 '{cmd_str}' 执行失败，返回码 {e.returncode}.",
                                 parent=self.gateway_tab_frame)
        except FileNotFoundError as e:
            status_label_widget.config(text="找不到文件！")
            self.append_output(output_widget, f"错误: 找不到文件 {e.filename}\n")
            messagebox.showerror("错误 - 网关", f"找不到文件！\n{e}", parent=self.gateway_tab_frame)
        except Exception as e:
            status_label_widget.config(text="发生未知错误！")
            self.append_output(output_widget, f"未知错误: {str(e)}\n")
            self.append_output(output_widget, traceback.format_exc() + "\n")
            messagebox.showerror("错误 - 网关", f"发生未知错误: {e}", parent=self.gateway_tab_frame)
        finally:
            start_button_widget.config(state="normal")

    def _clear_text_widget(self, text_widget):
        text_widget.config(state='normal')
        text_widget.delete('1.0', tk.END)
        text_widget.config(state='disabled')

    def _create_clear_context_menu(self, text_widget):
        context_menu = tk.Menu(self, tearoff=0)
        context_menu.add_command(label="清空显示", command=lambda: self._clear_text_widget(text_widget))
        return context_menu

    def _show_context_menu(self, event, context_menu):
        context_menu.tk_popup(event.x_root, event.y_root)

    def _read_process_output(self, process, output_widget):
        """
        实时读取进程输出并更新到GUI
        """
        for line in iter(process.stdout.readline, ''):
            if line:  # 避免空行
                # 直接在当前线程更新GUI，因为_gateway_flash_process已经在单独线程中运行
                self.append_output(output_widget, line)
                # 更新GUI事件循环，确保显示是即时的
                self.update_idletasks()
        # 确保进程完成并获取返回码
        process.wait()
        return process.returncode

    # Method to update hypsensor/config.ini
    def _update_hypsensor_config(self, com_port, baud_rate, mcuboot_tool_name):
        hypsensor_config_path = resource_path("hypsensor/config.ini")
        output_widget_for_log = self.sensor_output_text  # Log to sensor tab for this action

        try:
            if not os.path.exists(hypsensor_config_path):
                self.append_output(output_widget_for_log,
                                   f"警告: hypsensor配置文件 '{hypsensor_config_path}' 未找到。无法同步设置。")
                return

            with open(hypsensor_config_path, 'r') as f:
                lines = f.readlines()

            new_lines = []
            updated_keys = set()

            for line in lines:
                stripped_line = line.strip()
                if not stripped_line:  # Keep empty lines
                    new_lines.append(line)
                    continue

                key_value = stripped_line.split('=', 1)
                if len(key_value) == 2:
                    key = key_value[0].strip()
                    # value = key_value[1].strip() # We don't need the old value

                    if key == "COM_PORT":
                        new_lines.append(f"COM_PORT={com_port}\n")
                        updated_keys.add(key)
                    elif key == "BAUD_RATE":
                        new_lines.append(f"BAUD_RATE={baud_rate}\n")
                        updated_keys.add(key)
                    elif key == "mcuboot":  # Assuming this is the key in hypsensor/config.ini
                        new_lines.append(f"mcuboot={mcuboot_tool_name}\n")
                        updated_keys.add(key)
                    else:
                        new_lines.append(line)  # Keep other lines as is
                else:
                    new_lines.append(line)  # Keep lines not in key=value format (e.g. comments)

            # Add keys if they were not found and we want to ensure they exist
            # For now, strict "keep format" means only updating existing keys.
            # If you want to add missing keys, uncomment and adapt below:
            # if "COM_PORT" not in updated_keys: new_lines.append(f"COM_PORT={com_port}\n")
            # if "BAUD_RATE" not in updated_keys: new_lines.append(f"BAUD_RATE={baud_rate}\n")
            # if "mcuboot" not in updated_keys: new_lines.append(f"mcuboot={mcuboot_tool_name}\n")

            with open(hypsensor_config_path, 'w') as f:
                f.writelines(new_lines)
            self.append_output(output_widget_for_log, f"Info: hypsensor配置文件 '{hypsensor_config_path}' 已同步更新。")

        except Exception as e:
            self.append_output(output_widget_for_log,
                               f"错误: 更新hypsensor配置文件 '{hypsensor_config_path}' 失败: {e}")

    # def _check_and_create_id_file(self, file_path, file_description_for_user): # Removed/Commented out
    #     if not os.path.exists(file_path):
    #         try:
    #                                  parent=self)

    def _browse_id_file(self, id_type):
        # Determine initial directory based on id_type
        if id_type == 'sensor':
            initial_dir = os.path.dirname(self.sensor_id_file_path.get()) if self.sensor_id_file_path.get() else "."
        elif id_type == 'sensor_calib':
            initial_dir = os.path.dirname(self.sensor_calib_id_file_path.get()) if self.sensor_calib_id_file_path.get() else "."
        else:  # gateway
            initial_dir = os.path.dirname(self.gateway_id_file_path.get()) if self.gateway_id_file_path.get() else "."

        # Determine title based on id_type
        if id_type == 'sensor':
            title = "选择 传感器 ID 文件"
        elif id_type == 'sensor_calib':
            title = "选择 传感器标定 ID 文件"
        else:  # gateway
            title = "选择 网关 ID 文件"

        file_path = filedialog.askopenfilename(
            title=title,
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            initialdir=initial_dir if os.path.isdir(initial_dir) else "."
        )

        if not file_path:
            return  # User cancelled

        if id_type == 'sensor':
            ids_list_ref = self.sensor_ids_list
            id_var = self.sensor_id_var
            id_file_path_var = self.sensor_id_file_path
            output_widget = self.sensor_output_text
            set_current_index_func = lambda index: setattr(self, 'sensor_current_id_index', index)
            get_current_index_func = lambda: self.sensor_current_id_index
            update_buttons_func = lambda: self._update_id_navigation_buttons_state(
                self.sensor_ids_list, self.sensor_current_id_index, self.sensor_prev_id_btn, self.sensor_next_id_btn
            )
        elif id_type == 'sensor_calib':
            ids_list_ref = self.sensor_calib_ids_list
            id_var = self.sensor_calib_id_var
            id_file_path_var = self.sensor_calib_id_file_path
            output_widget = self.sensor_calib_output_text
            set_current_index_func = lambda index: setattr(self, 'sensor_calib_current_id_index', index)
            get_current_index_func = lambda: self.sensor_calib_current_id_index
            update_buttons_func = lambda: self._update_id_navigation_buttons_state(
                self.sensor_calib_ids_list, self.sensor_calib_current_id_index, self.sensor_calib_prev_id_btn, self.sensor_calib_next_id_btn
            )
        elif id_type == 'gateway':
            ids_list_ref = self.gateway_ids_list
            id_var = self.gateway_id_var
            id_file_path_var = self.gateway_id_file_path
            output_widget = self.gateway_output_text
            set_current_index_func = lambda index: setattr(self, 'gateway_current_id_index', index)
            get_current_index_func = lambda: self.gateway_current_id_index
            update_buttons_func = lambda: self._update_id_navigation_buttons_state(
                self.gateway_ids_list, self.gateway_current_id_index, self.gateway_prev_id_btn, self.gateway_next_id_btn
            )
        else:
            return  # Should not happen

        id_file_path_var.set(file_path)
        self.append_output(output_widget, f"Info: 已选择ID文件: {file_path}\n")

        # Now load the IDs from the selected file
        loaded_successfully, current_id_index_val = self._load_ids_from_txt(file_path, ids_list_ref, output_widget)

        # Reset index and potentially clear ID var before setting based on load result
        set_current_index_func(-1)
        id_var.set("")

        if loaded_successfully and ids_list_ref:
            set_current_index_func(0)  # Default to the first ID in the newly loaded list
            id_var.set(ids_list_ref[0])

        update_buttons_func()  # Update button states based on the newly loaded list
        self._update_id_counts_display()  # 更新ID总数显示
        self.save_config()  # Save the newly selected path

    def _create_id_generation_tab(self):
        parent_tab_frame = self.id_gen_tab_frame

        main_frame = ttk.Frame(parent_tab_frame, padding="20 20 20 20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        parent_tab_frame.columnconfigure(0, weight=1)
        parent_tab_frame.rowconfigure(0, weight=1)

        ttk.Label(main_frame, text="选择型号:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.id_gen_model_var = tk.StringVar()
        self.id_gen_model_combo = ttk.Combobox(main_frame, textvariable=self.id_gen_model_var, width=27)
        self.id_gen_model_combo['values'] = list(DEVICE_MODELS.keys())  # Use the migrated DEVICE_MODELS
        self.id_gen_model_combo['state'] = 'readonly'
        self.id_gen_model_combo.grid(row=0, column=1, padx=5, pady=5, sticky=tk.EW)
        if self.id_gen_model_combo['values']:
            self.id_gen_model_combo.current(0)

        ttk.Label(main_frame, text="生成数量:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.id_gen_count_var = tk.StringVar()
        self.id_gen_count_entry = ttk.Entry(main_frame, textvariable=self.id_gen_count_var, width=30)
        self.id_gen_count_entry.grid(row=1, column=1, padx=5, pady=5, sticky=tk.EW)

        ttk.Label(main_frame, text="指定日期 (YYYY-MM-DD):").grid(row=2, column=0, padx=5, pady=5, sticky=tk.W)
        self.id_gen_date_var = tk.StringVar()
        self.id_gen_date_entry = ttk.Entry(main_frame, textvariable=self.id_gen_date_var, width=30)
        self.id_gen_date_entry.grid(row=2, column=1, padx=5, pady=5, sticky=tk.EW)

        self.id_gen_generate_btn = ttk.Button(main_frame, text="生成ID", command=self._handle_id_generation)
        self.id_gen_generate_btn.grid(row=3, column=0, columnspan=2, padx=5, pady=10)

        self.id_gen_status_label = ttk.Label(main_frame, text="请选择型号并输入数量。", wraplength=350)
        self.id_gen_status_label.grid(row=4, column=0, columnspan=2, padx=5, pady=10)

        main_frame.columnconfigure(1, weight=1)

    def _handle_id_generation(self):
        selected_model = self.id_gen_model_var.get()
        count_str = self.id_gen_count_var.get()
        date_str = self.id_gen_date_var.get().strip()
        parsed_target_date = None

        if not selected_model:
            self.id_gen_status_label.config(text="错误: 请选择一个设备型号。", foreground="red")
            return

        try:
            count = int(count_str)
            if count <= 0:
                self.id_gen_status_label.config(text="错误: 生成数量必须是正整数。", foreground="red")
                return
        except ValueError:
            self.id_gen_status_label.config(text="错误: 生成数量必须是有效的数字。", foreground="red")
            return

        if date_str:  # If user entered something for date
            try:
                parsed_target_date = datetime.strptime(date_str, "%Y-%m-%d")
            except ValueError:
                self.id_gen_status_label.config(text="错误: 日期格式无效。请输入 YYYY-MM-DD 或留空。", foreground="red")
                return

        # Call the migrated generation function (now a top-level function)
        success, message = generate_device_ids_to_file(count, selected_model, target_date=parsed_target_date)

        if success:
            self.id_gen_status_label.config(text=message, foreground="green")

    def _create_qr_gen_tab(self):
        parent_tab_frame = self.qr_gen_tab_frame

        self.qr_gen_main_frame = ttk.Frame(parent_tab_frame, padding="10")  # Changed to instance attribute
        self.qr_gen_main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        parent_tab_frame.columnconfigure(0, weight=1)
        parent_tab_frame.rowconfigure(0, weight=1)

        # Source ID File Selection
        ttk.Label(self.qr_gen_main_frame, text="ID 文件 (.txt):").grid(row=0, column=0, padx=5, pady=5,
                                                                       sticky=tk.W)  # Used self.qr_gen_main_frame
        self.qr_source_file_var = tk.StringVar()
        qr_source_file_entry = ttk.Entry(self.qr_gen_main_frame, textvariable=self.qr_source_file_var, state="readonly",
                                         width=50)  # Used self.qr_gen_main_frame
        qr_source_file_entry.grid(row=0, column=1, padx=5, pady=5, sticky=tk.EW)
        ttk.Button(self.qr_gen_main_frame, text="选择文件", command=self._browse_qr_source_file).grid(row=0, column=2,
                                                                                                      padx=5,
                                                                                                      pady=5)  # Used self.qr_gen_main_frame

        # ID Type Input - Changed to Combobox
        ttk.Label(self.qr_gen_main_frame, text="ID 类型:").grid(row=1, column=0, padx=5, pady=5,
                                                                sticky=tk.W)  # Used self.qr_gen_main_frame
        self.qr_id_type_var = tk.StringVar()
        self.qr_id_type_combo = ttk.Combobox(self.qr_gen_main_frame, textvariable=self.qr_id_type_var,
                                             # Used self.qr_gen_main_frame
                                             values=["HYP60", "HYP225", "HYPX", "HYPLINK"],
                                             state="readonly", width=28)
        self.qr_id_type_combo.grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        if self.qr_id_type_combo['values']:
            self.qr_id_type_combo.current(0)

        # Save Directory Selection
        ttk.Label(self.qr_gen_main_frame, text="保存目录:").grid(row=2, column=0, padx=5, pady=5,
                                                                 sticky=tk.W)  # Used self.qr_gen_main_frame
        self.qr_save_dir_var = tk.StringVar()
        qr_save_dir_entry = ttk.Entry(self.qr_gen_main_frame, textvariable=self.qr_save_dir_var, state="readonly",
                                      width=50)  # Used self.qr_gen_main_frame
        qr_save_dir_entry.grid(row=2, column=1, padx=5, pady=5, sticky=tk.EW)
        ttk.Button(self.qr_gen_main_frame, text="选择目录", command=self._browse_qr_save_directory).grid(row=2,
                                                                                                         column=2,
                                                                                                         padx=5,
                                                                                                         pady=5)  # Used self.qr_gen_main_frame

        # Start Button
        self.qr_generate_btn = ttk.Button(self.qr_gen_main_frame, text="开始生成QR码",
                                          command=self._start_qr_generation_thread)
        self.qr_generate_btn.grid(row=3, column=0, columnspan=3, padx=5, pady=5)  # Adjusted pady to 5

        # Batch Generate Button
        self.qr_batch_generate_btn = ttk.Button(self.qr_gen_main_frame, text="批量生成 (选择目录)",
                                                command=self._start_batch_qr_generation_thread)
        self.qr_batch_generate_btn.grid(row=4, column=0, columnspan=3, padx=5, pady=10)

        # Export to Excel Button
        self.qr_export_excel_btn = ttk.Button(self.qr_gen_main_frame, text="导出Excel",
                                              command=self._start_export_to_excel_thread)
        self.qr_export_excel_btn.grid(row=5, column=0, columnspan=3, padx=5, pady=5)
        if not PANDAS_AVAILABLE:
            self.qr_export_excel_btn.config(state="disabled", text="导出Excel (缺少pandas)")

        # Status Label
        self.qr_status_label = ttk.Label(self.qr_gen_main_frame, text="请配置以上选项并开始生成。", wraplength=450)
        self.qr_status_label.grid(row=6, column=0, columnspan=3, padx=5, pady=10)  # Adjusted row

        # --- Add QR Decoder UI ---
        ttk.Separator(self.qr_gen_main_frame, orient=tk.HORIZONTAL).grid(row=7, column=0, columnspan=3, sticky="ew",
                                                                         pady=15)

        ttk.Label(self.qr_gen_main_frame, text="QR解码验证:").grid(row=8, column=0, padx=5, pady=5, sticky=tk.W)

        ttk.Label(self.qr_gen_main_frame, text="输入QR值:").grid(row=9, column=0, padx=5, pady=5, sticky=tk.W)
        self.qr_decode_input_var = tk.StringVar()
        qr_decode_input_entry = ttk.Entry(self.qr_gen_main_frame, textvariable=self.qr_decode_input_var, width=50)
        qr_decode_input_entry.grid(row=9, column=1, padx=5, pady=5, sticky=tk.EW)

        self.qr_decode_btn = ttk.Button(self.qr_gen_main_frame, text="解码", command=self._handle_qr_decode)
        self.qr_decode_btn.grid(row=9, column=2, padx=5, pady=5)

        ttk.Label(self.qr_gen_main_frame, text="解码结果:").grid(row=10, column=0, padx=5, pady=5, sticky=tk.W)
        self.qr_decode_result_var = tk.StringVar()
        qr_decode_result_label = ttk.Label(self.qr_gen_main_frame, textvariable=self.qr_decode_result_var,
                                           foreground="blue", wraplength=400)
        qr_decode_result_label.grid(row=10, column=1, columnspan=2, padx=5, pady=5, sticky=tk.W)

        self.qr_gen_main_frame.columnconfigure(1, weight=1)

    def _browse_qr_source_file(self):
        filepath = filedialog.askopenfilename(filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                                              title="选择包含ID的文本文件")
        if filepath:
            self.qr_source_file_var.set(filepath)

            # Set default save directory
            default_save_dir = os.path.dirname(filepath)
            self.qr_save_dir_var.set(default_save_dir)

            # Auto-select ID type based on filename
            filename_lower = os.path.basename(filepath).lower()

            # Get defined Combobox values for validation
            valid_id_types = list(self.qr_id_type_combo['values'])
            potential_selected_type = ""  # Use a temporary variable for clarity

            if "hyp60" in filename_lower:
                potential_selected_type = "HYP60"
            elif "hyp225" in filename_lower:
                potential_selected_type = "HYP225"
            elif "hypx" in filename_lower:
                potential_selected_type = "HYPX"
            elif "gateway" in filename_lower or "hyplink" in filename_lower:
                potential_selected_type = "HYPLINK"

            current_type_selection_made = False  # Default to False
            if potential_selected_type and potential_selected_type in valid_id_types:
                self.qr_id_type_var.set(potential_selected_type)
                current_type_selection_made = True
            # If no match or an invalid type was somehow derived (e.g. from a typo in keywords),
            # qr_id_type_var remains as is (likely its previous value or Combobox default),
            # and current_type_selection_made remains False, prompting user to confirm.

            status_msg_type = f"ID类型自动选择: {self.qr_id_type_var.get() if current_type_selection_made else '请确认类型'}"

            # Get the currently selected or auto-selected ID type for display path
            current_id_type_for_path = self.qr_id_type_var.get()
            # Sanitize it for display path just in case, though it's from a combobox
            sanitized_id_type_for_path = sanitize_filename(
                current_id_type_for_path) if current_id_type_for_path else "selected_type"

            target_qr_type_dir_display = os.path.join(default_save_dir, "QR", sanitized_id_type_for_path)

            self.qr_status_label.config(
                text=f"ID文件: {os.path.basename(filepath)}\n计划保存到: {target_qr_type_dir_display}\n{status_msg_type}",
                wraplength=self.qr_gen_main_frame.winfo_width() - 20
            )

    def _browse_qr_save_directory(self):
        dirpath = filedialog.askdirectory(title="选择QR码保存目录")
        if dirpath:
            self.qr_save_dir_var.set(dirpath)
            self.qr_status_label.config(text=f"已选择保存目录: {dirpath}")

    def _handle_qr_decode(self):
        qr_value = self.qr_decode_input_var.get().strip()
        if not qr_value:
            self.qr_decode_result_var.set("请输入QR码内容")
            return

        try:
            decoded_str = qr_decode_full_str(qr_value)
            self.qr_decode_result_var.set(f"解码成功: {decoded_str}")
        except Exception as e:
            self.qr_decode_result_var.set(f"解码失败: {str(e)}")

    def _start_qr_generation_thread(self):
        self.qr_generate_btn.config(state="disabled")
        self.qr_status_label.config(text="准备开始...")
        threading.Thread(target=self._handle_qr_generation_process, daemon=True).start()

    def _process_single_id_file_for_qr(self, source_id_file: str, id_type_str: str, output_qr_directory: str) -> tuple[
        int, int, int]:
        """Processes a single ID file to generate QR codes.
        Args:
            source_id_file: Path to the source .txt file containing IDs.
            id_type_str: The type of ID (e.g., "HYPX") for encoding.
            output_qr_directory: The directory where .png QR code images will be saved.
        Returns:
            A tuple (successful_saves, failed_saves, ids_processed_count).
        """
        successful_saves = 0
        failed_saves = 0
        ids_processed_count = 0

        try:
            with open(source_id_file, 'r') as f:
                for line_num, line in enumerate(f):
                    original_id = line.strip()
                    if not original_id:
                        continue  # Skip empty lines

                    ids_processed_count += 1
                    # Optional: temporary status update if needed within a long batch, 
                    # but primary status should be handled by caller.
                    # print(f"Processing ID {original_id} for {id_type_str} into {output_qr_directory}")

                    encoded_id = qr_encode_id_with_type(original_id, id_type_str)
                    success_one_image = self._generate_qr_image_with_text(original_id, encoded_id, output_qr_directory)
                    if success_one_image:
                        successful_saves += 1
                    else:
                        failed_saves += 1
            return successful_saves, failed_saves, ids_processed_count
        except FileNotFoundError:
            # Log to console, caller will handle summary status for UI
            print(f"Error: Source file not found during single file processing - {source_id_file}")
            return 0, ids_processed_count, ids_processed_count  # All attempted are failures if file not found
        except Exception as e:
            # Log to console, caller will handle summary status for UI
            print(f"Error processing ID file '{source_id_file}': {e}\n{traceback.format_exc()}")
            # Assume all processed up to this point (if any) might be unknown, count all as failed if error is mid-file
            return successful_saves, ids_processed_count - successful_saves, ids_processed_count

    def _handle_qr_generation_process(self):
        source_file = self.qr_source_file_var.get()
        id_type = self.qr_id_type_var.get().strip()
        base_save_dir = self.qr_save_dir_var.get()  # This is the directory of the ID file

        if not source_file:
            self.qr_status_label.config(text="错误: 请选择ID源文件。", foreground="red")
            self.qr_generate_btn.config(state="normal")
            return
        if not id_type:
            self.qr_status_label.config(text="错误: 请输ID类型。", foreground="red")
            self.qr_generate_btn.config(state="normal")
            return
        if not base_save_dir:  # Check if base_save_dir (from ID file selection) is set
            self.qr_status_label.config(text="错误: 请选择ID文件以确定保存目录结构。", foreground="red")
            self.qr_generate_btn.config(state="normal")
            return

        qr_specific_save_dir = os.path.join(base_save_dir, "QR")

        try:
            os.makedirs(qr_specific_save_dir, exist_ok=True)
        except OSError as e:
            self.qr_status_label.config(text=f"错误: 无法创建QR保存目录 '{qr_specific_save_dir}'. 错误: {e}",
                                        foreground="red")
            self.qr_generate_btn.config(state="normal")
            return

        # Create type-specific subfolder
        id_type_for_folder = sanitize_filename(id_type)  # Sanitize the id_type for use as a folder name
        if not id_type_for_folder:  # handle empty or fully sanitized id_type
            id_type_for_folder = "default_type"  # Fallback folder name

        final_save_dir = os.path.join(qr_specific_save_dir, id_type_for_folder)

        try:
            os.makedirs(final_save_dir, exist_ok=True)
        except OSError as e:
            self.qr_status_label.config(text=f"错误: 无法创建类型特定QR保存目录 '{final_save_dir}'. 错误: {e}",
                                        foreground="red")
            self.qr_generate_btn.config(state="normal")
            return

        # Delete old QR codes in the target directory before generating new ones
        self.qr_status_label.config(text=f"正在清除旧的QR码于 {final_save_dir}...", foreground="blue")
        self.update_idletasks()  # Ensure UI updates for the status message
        try:
            old_qr_files = glob.glob(os.path.join(final_save_dir, "*.png"))
            if old_qr_files:
                for qr_file in old_qr_files:
                    try:
                        os.remove(qr_file)
                    except OSError as e_remove:
                        # Log to console, but don't stop the whole process for one failed delete
                        print(f"Warning: Could not delete old QR file '{qr_file}'. Error: {e_remove}")
                self.qr_status_label.config(text=f"旧QR码已清除。开始生成新的QR码于 {final_save_dir}...",
                                            foreground="blue")
            else:
                self.qr_status_label.config(text=f"无需清除旧QR码。开始生成新的QR码于 {final_save_dir}...",
                                            foreground="blue")
            self.update_idletasks()
        except Exception as e_glob:
            # Error during globbing or initial status update phase, less critical to halt but good to log
            print(f"Warning: Error during pre-generation cleanup phase in '{final_save_dir}'. Error: {e_glob}")
            self.qr_status_label.config(text=f"警告: 清理旧文件时出错。继续生成...", foreground="orange")
            self.update_idletasks()

        try:
            self.qr_status_label.config(text="正在处理...", foreground="blue")  # This will overwrite the cleanup status
            # Call the refactored processing function
            s_count, f_count, a_count = self._process_single_id_file_for_qr(
                source_file, id_type, final_save_dir
            )

            # Update UI based on the results from the refactored function
            if f_count == 0 and a_count > 0:
                self.qr_status_label.config(text=f"完成！成功生成 {s_count} 个QR码到 \n{final_save_dir}",
                                            foreground="green")
            elif a_count == 0:
                self.qr_status_label.config(text=f"未处理任何ID。请检查ID文件 ({os.path.basename(source_file)}).",
                                            foreground="orange")
            else:  # Some failures or mix
                self.qr_status_label.config(
                    text=f"处理完成。成功: {s_count}, 失败: {f_count} (共尝试 {a_count} 个).\n保存到: {final_save_dir}\n部分文件可能生成失败，请检查控制台输出。",
                    foreground="orange")

            # No direct try-except block here for FileNotFoundError or general Exception for _process_single_id_file_for_qr
            # as those are now handled inside _process_single_id_file_for_qr and logged to console.
            # The status label update above handles the summary.

            # The overarching try-except for directory creation or initial setup errors remains if needed,
            # but the main file processing errors are now self-contained in the helper.

            # Ensure button is re-enabled in the existing finally block of the caller
            # (which is this function, and it has a finally block already)

        except FileNotFoundError:
            self.qr_status_label.config(text=f"错误: 源文件未找到 - {source_file}", foreground="red")
        except Exception as e:  # Ensure this is at the same indentation level as FileNotFoundError
            self.qr_status_label.config(text=f"生成QR码时发生错误: {e}", foreground="red")
            print(traceback.format_exc())  # Print full traceback for broader errors
        finally:
            self.qr_generate_btn.config(state="normal")

    def _generate_qr_image_with_text(self, original_id, encoded_id, save_dir):
        sanitized_original_id = sanitize_filename(original_id)
        output_filename = f"{sanitized_original_id}.png"
        output_path = os.path.join(save_dir, output_filename)

        try:
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=1  # User changed to 1
            )
            qr.add_data(encoded_id)
            qr.make(fit=True)
            img_qr = qr.make_image(fill_color="black", back_color="white").convert('RGB')

            try:
                font = ImageFont.truetype("arial.ttf", 20)  # Increased font size
            except IOError:
                font = ImageFont.load_default()  # Fallback
                if hasattr(font, 'size'):  # Check if default font has size attribute (Pillow >= 9.0.0)
                    # Try to get a slightly larger default if possible, crude way:
                    try:
                        font = ImageFont.truetype(font.path, font.size + 5)
                    except:
                        pass  # If path not available or fails, stick to original default
                # Else for very old Pillow, default font size is fixed, this is best effort

            # Create a temporary draw object on the QR to measure text
            temp_draw = ImageDraw.Draw(img_qr)
            if hasattr(temp_draw, 'textbbox'):  # Pillow 9.2.0+ for more accurate bbox
                # (x0, y0, x1, y1) bounding box
                text_bbox = temp_draw.textbbox((0, 0), original_id, font=font, anchor="lt")  # anchor might be useful
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]
            else:  # Fallback for older Pillow versions
                text_width, text_height = temp_draw.textsize(original_id, font=font)

            text_padding_vertical = 15  # Kept at 15
            qr_width, qr_height = img_qr.size

            # Ensure final image is wide enough for text if text is wider than QR
            final_width = max(qr_width, text_width + 20)  # Add some horizontal padding for text too
            final_height = qr_height + text_height + text_padding_vertical

            final_img = Image.new('RGB', (final_width, final_height), 'white')

            # Paste QR code, centered if final_width is larger
            qr_paste_x = (final_width - qr_width) // 2
            final_img.paste(img_qr, (qr_paste_x, 0))

            draw_final = ImageDraw.Draw(final_img)
            # Center the text horizontally
            text_x = (final_width - text_width) / 2
            # text_y = qr_height # User had changed to this
            text_y = qr_height - (text_height // 4)  # User changed to this

            draw_final.text((text_x, text_y), original_id, fill="black", font=font, anchor=None)

            final_img.save(output_path)
            return True  # Indicate success

        except Exception as e:
            error_msg_detail = f"错误: 生成QR码 '{original_id}' (保存为 '{output_filename}') 失败: {e}\n{traceback.format_exc()}"
            print(error_msg_detail)  # Print detailed error to console
            # Update status label with a general per-image error
            # This might be too chatty if many errors; handled by overall summary now.
            # self.qr_status_label.config(text=f"处理 '{original_id}' 时出错。查看控制台。", foreground="orange")
            # self.update_idletasks()
            return False  # Indicate failure

    def _start_batch_qr_generation_thread(self):
        self.qr_batch_generate_btn.config(state="disabled")
        self.qr_status_label.config(text="准备开始批量生成...", foreground="blue")
        self.update_idletasks()
        threading.Thread(target=self._handle_batch_qr_generation_process, daemon=True).start()

    def _handle_batch_qr_generation_process(self):
        root_dir = filedialog.askdirectory(title="选择包含ID文件的根目录")
        if not root_dir:
            self.qr_status_label.config(text="批量生成已取消 (未选择目录)。", foreground="orange")
            self.qr_batch_generate_btn.config(state="normal")
            return

        self.qr_status_label.config(text=f"扫描目录: {root_dir}...", foreground="blue")
        self.update_idletasks()

        found_id_files = []
        for dirpath, _, filenames in os.walk(root_dir):
            for filename in filenames:
                # Consider only .txt files, or a more specific pattern if needed e.g. "*_ids.txt"
                if filename.lower().endswith(".txt"):
                    # Avoid processing files within any "QR" subdirectories themselves
                    if "QR" not in dirpath.split(os.sep) and "qr" not in dirpath.split(os.sep):
                        found_id_files.append(os.path.join(dirpath, filename))

        if not found_id_files:
            self.qr_status_label.config(text=f"在 '{root_dir}' 未找到ID文件 (.txt)。", foreground="orange")
            self.qr_batch_generate_btn.config(state="normal")
            return

        self.qr_status_label.config(text=f"找到 {len(found_id_files)} 个ID文件。开始批量处理...", foreground="blue")
        self.update_idletasks()

        total_s_count = 0
        total_f_count = 0
        total_a_count = 0
        files_processed_count = 0

        valid_id_types_for_batch = list(self.qr_id_type_combo['values'])

        for id_file_path in found_id_files:
            files_processed_count += 1
            self.qr_status_label.config(
                text=f"处理文件 {files_processed_count}/{len(found_id_files)}: {os.path.basename(id_file_path)}...",
                foreground="blue"
            )
            self.update_idletasks()

            # 1. Infer ID Type
            filename_lower_batch = os.path.basename(id_file_path).lower()
            batch_id_type = ""
            if "hyp60" in filename_lower_batch:
                batch_id_type = "HYP60"
            elif "hyp225" in filename_lower_batch:
                batch_id_type = "HYP225"
            elif "hypx" in filename_lower_batch:
                batch_id_type = "HYPX"
            elif "gateway" in filename_lower_batch or "hyplink" in filename_lower_batch:
                batch_id_type = "HYPLINK"

            if not batch_id_type or batch_id_type not in valid_id_types_for_batch:
                print(
                    f"Warning:无法从文件名 '{os.path.basename(id_file_path)}' 推断有效ID类型或类型不受支持。跳过此文件。")
                # Optionally update status label about skipping, or just log to console
                continue  # Skip this file

            # 2. Determine Save Directory (root_dir/QR/<ID_TYPE>/)
            batch_qr_base_dir = os.path.join(root_dir, "QR")
            batch_id_type_for_folder = sanitize_filename(batch_id_type)
            if not batch_id_type_for_folder: batch_id_type_for_folder = "unknown_type"  # Fallback

            batch_final_save_dir = os.path.join(batch_qr_base_dir, batch_id_type_for_folder)
            try:
                os.makedirs(batch_final_save_dir, exist_ok=True)
            except OSError as e_mkdir_batch:
                print(
                    f"Error creating directory '{batch_final_save_dir}' for '{id_file_path}'. Skipping. Error: {e_mkdir_batch}")
                continue  # Skip this file

            # 3. Delete old QR codes in this specific batch_final_save_dir
            try:
                old_qr_files_batch = glob.glob(os.path.join(batch_final_save_dir, "*.png"))
                if old_qr_files_batch:
                    # print(f"Cleaning {len(old_qr_files_batch)} old QR files from {batch_final_save_dir}")
                    for qr_file_b in old_qr_files_batch:
                        try:
                            os.remove(qr_file_b)
                        except OSError as e_remove_b:
                            print(f"Warning: Could not delete old QR file '{qr_file_b}'. Error: {e_remove_b}")
            except Exception as e_glob_b:
                print(f"Warning: Error during pre-generation cleanup for '{batch_final_save_dir}'. Error: {e_glob_b}")

            # 4. Process the single ID file
            s, f, a = self._process_single_id_file_for_qr(id_file_path, batch_id_type, batch_final_save_dir)
            total_s_count += s
            total_f_count += f
            total_a_count += a

        # Final status update for batch process
        if total_a_count == 0 and len(found_id_files) > 0:
            # This case means all files found were skipped before processing (e.g. bad type)
            self.qr_status_label.config(
                text=f"批量处理完成。未处理任何有效ID（共找到{len(found_id_files)}个文件）。请检查文件名或控制台日志。",
                foreground="orange")
        elif total_a_count == 0 and not found_id_files:
            # This is already handled by the "未找到ID文件" message earlier, but as a safeguard.
            self.qr_status_label.config(text="批量处理：未找到或未处理任何文件。", foreground="orange")
        elif total_f_count == 0 and total_a_count > 0:
            self.qr_status_label.config(text=f"批量处理全部完成！成功生成 {total_s_count} 个QR码。", foreground="green")
        else:
            self.qr_status_label.config(
                text=f"批量处理完成。总成功: {total_s_count}, 总失败: {total_f_count} (共尝试 {total_a_count} 个ID from {files_processed_count} files).\n部分文件可能生成失败，请检查控制台输出。",
                foreground="orange"
            )

        self.qr_batch_generate_btn.config(state="normal")

    def _start_export_to_excel_thread(self):
        self.qr_export_excel_btn.config(state="disabled")
        self.qr_status_label.config(text="准备导出到Excel...")
        threading.Thread(target=self._handle_export_to_excel_process, daemon=True).start()

    def _handle_export_to_excel_process(self):
        try:
            if not PANDAS_AVAILABLE:
                self.qr_status_label.config(text="错误: pandas 库未安装，无法导出Excel。", foreground="red")
                messagebox.showerror("依赖缺失", "需要 'pandas' 和 'openpyxl' 库来导出Excel。\n请运行: pip install pandas openpyxl", parent=self.qr_gen_tab_frame)
                return

            output_path = filedialog.asksaveasfilename(
                title="保存Excel文件",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                defaultextension=".xlsx",
                parent=self.qr_gen_tab_frame
            )
            if not output_path:
                self.qr_status_label.config(text="导出操作已取消。", foreground="orange")
                return

            self.qr_status_label.config(text="正在收集数据...", foreground="blue")
            self.update_idletasks()

            id_sources = {
                "hyp60_ids.txt": "HYP60",
                "hyp225_ids.txt": "HYP225",
                "hypx_ids.txt": "HYPX",
                "gateway_ids.txt": "HYPLINK"
            }

            all_data = []
            files_not_found = []

            for filename, id_type in id_sources.items():
                try:
                    file_path = resource_path(filename)
                    if not os.path.exists(file_path):
                        files_not_found.append(filename)
                        continue

                    with open(file_path, 'r', encoding='utf-8') as f:
                        for line in f:
                            original_id = line.strip()
                            if original_id:
                                encoded_id = qr_encode_id_with_type(original_id, id_type)
                                all_data.append({
                                    "Original ID": original_id,
                                    "Encoded ID": encoded_id
                                })
                except Exception as e:
                    print(f"处理文件 {filename} 时出错: {e}")
                    self.qr_status_label.config(text=f"处理 {filename} 时出错，已跳过。", foreground="orange")
                    self.update_idletasks()

            if not all_data:
                msg = "未找到任何ID数据进行导出。"
                if files_not_found:
                    msg += f"\n以下文件未找到: {', '.join(files_not_found)}"
                self.qr_status_label.config(text=msg, foreground="red")
                return

            self.qr_status_label.config(text="正在生成Excel文件...", foreground="blue")
            self.update_idletasks()

            df = pd.DataFrame(all_data)
            df.to_excel(output_path, index=False, engine='openpyxl')

            final_msg = f"成功导出 {len(all_data)} 条记录到\n{output_path}"
            if files_not_found:
                final_msg += f"\n\n警告: 未找到以下文件: {', '.join(files_not_found)}"
            self.qr_status_label.config(text=final_msg, foreground="green")

        except Exception as e:
            self.qr_status_label.config(text=f"导出Excel时发生错误: {e}", foreground="red")
            print(traceback.format_exc())
            messagebox.showerror("导出错误", f"导出Excel时发生未知错误:\n{e}", parent=self.qr_gen_tab_frame)
        finally:
            self.qr_export_excel_btn.config(state="normal")

    def _jump_to_id_index(self, ids_list_ref, id_var, jump_index_var, set_current_index_func, 
                          get_current_index_func, update_buttons_func, output_widget):
        """跳转到指定序号的ID"""
        try:
            index_str = jump_index_var.get().strip()
            if not index_str:
                return
                
            index = int(index_str) - 1  # 用户输入从1开始，但索引从0开始
            
            if not ids_list_ref:
                self.append_output(output_widget, "错误: ID列表为空，无法跳转。\n")
                return
                
            if index < 0 or index >= len(ids_list_ref):
                self.append_output(output_widget, f"错误: 序号超出范围(1-{len(ids_list_ref)})。\n")
                # 重置为当前有效序号
                current_idx = get_current_index_func()
                if current_idx >= 0 and current_idx < len(ids_list_ref):
                    jump_index_var.set(str(current_idx + 1))
                return
                
            set_current_index_func(index)
            id_var.set(ids_list_ref[index])
            update_buttons_func()
            
        except ValueError:
            self.append_output(output_widget, "错误: 请输入有效的序号。\n")
            # 重置为当前有效序号
            current_idx = get_current_index_func()
            if current_idx >= 0 and current_idx < len(ids_list_ref):
                jump_index_var.set(str(current_idx + 1))

    def _update_id_counts_display(self):
        """更新ID总数显示"""
        if hasattr(self, 'sensor_total_count_label'):
            self.sensor_total_count_label.config(text=f"/ {len(self.sensor_ids_list)}")

            # 更新当前序号显示
            if self.sensor_current_id_index >= 0 and self.sensor_current_id_index < len(self.sensor_ids_list):
                self.sensor_jump_index_var.set(str(self.sensor_current_id_index + 1))
            else:
                self.sensor_jump_index_var.set("")

        if hasattr(self, 'sensor_calib_total_count_label'):
            self.sensor_calib_total_count_label.config(text=f"/ {len(self.sensor_calib_ids_list)}")

            # 更新当前序号显示
            if self.sensor_calib_current_id_index >= 0 and self.sensor_calib_current_id_index < len(self.sensor_calib_ids_list):
                self.sensor_calib_jump_index_var.set(str(self.sensor_calib_current_id_index + 1))
            else:
                self.sensor_calib_jump_index_var.set("")

        if hasattr(self, 'gateway_total_count_label'):
            self.gateway_total_count_label.config(text=f"/ {len(self.gateway_ids_list)}")

            # 更新当前序号显示
            if self.gateway_current_id_index >= 0 and self.gateway_current_id_index < len(self.gateway_ids_list):
                self.gateway_jump_index_var.set(str(self.gateway_current_id_index + 1))
            else:
                self.gateway_jump_index_var.set("")

    def check_and_download_firmware(self, firmware_type=None, auto_mode=False):
        """检查并下载指定类型的固件"""
        # 如果没有指定类型，退出
        if not firmware_type or firmware_type not in self.FIRMWARE_S3_CONFIG:
            return
        
        # 获取URL
        url = self.FIRMWARE_S3_CONFIG[firmware_type]["url"]
        
        # 设置状态标签引用
        if firmware_type == "hyplink":
            status_label = self.gateway_firmware_status_label
        else:  # hypsensor
            status_label = self.sensor_firmware_status_label
        
        # 验证URL
        if not url:
            status_label.config(text="错误: 固件URL无效", foreground="red")
            return
        
        # 创建状态窗口
        status_window = tk.Toplevel(self)
        status_window.title("固件更新")
        status_window.geometry("400x150")
        status_window.resizable(False, False)
        status_window.transient(self)  # 设置为主窗口的临时子窗口
        
        # 居中显示
        status_window.geometry("+%d+%d" % (
            self.winfo_rootx() + self.winfo_width() // 2 - 200,
            self.winfo_rooty() + self.winfo_height() // 2 - 75
        ))
        
        # 添加状态标签和进度条
        window_status_label = ttk.Label(status_window, text=f"正在更新{firmware_type}固件...", font=("Arial", 10))
        window_status_label.pack(pady=10)
        
        progress = ttk.Progressbar(status_window, orient="horizontal", length=350, mode="determinate")
        progress.pack(pady=10)
        
        # 添加取消按钮
        cancel_button = ttk.Button(status_window, text="取消", command=status_window.destroy)
        cancel_button.pack(pady=10)
        
        # 更新UI
        self.update_idletasks()
        
        # 创建线程执行下载任务
        download_thread = threading.Thread(
            target=self._download_firmware_thread,
            args=(status_window, window_status_label, progress, firmware_type)
        )
        download_thread.daemon = True
        download_thread.start()

    def _download_firmware_thread(self, status_window, status_label, progress, firmware_type):
        """在后台线程中执行固件下载"""
        try:
            # 添加浏览器模拟头，解决可能的403错误，精简为关键头信息
            browser_headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': '*/*'
            }
            
            # 导入用于URL处理的库
            import urllib.parse
            
            # 检查网络连接
            status_label.config(text="正在检查网络连接...")
            self.update_idletasks()
            
            # 获取固件配置
            config = self.FIRMWARE_S3_CONFIG[firmware_type]
            local_path = config["local_path"]
            backup_path = config["backup_path"]
            temp_path = config["temp_path"]
            url = config["url"]
            
            # 设置UI引用
            if firmware_type == "hyplink":
                app_status_label = self.gateway_firmware_status_label
            else:  # hypsensor
                app_status_label = self.sensor_firmware_status_label
            
            # 确保URL编码正确
            try:
                # 处理URL以确保正确编码
                parsed_url = urllib.parse.urlparse(url)
                # 保持原始查询参数不变，只解析一下
                final_url = url
            except Exception as e:
                print(f"解析URL时出错: {e}")
                final_url = url  # 出错时使用原始URL
            
            # 检查本地文件是否存在
            local_exists = os.path.exists(local_path)
            
            # 获取远程文件信息
            try:
                status_label.config(text=f"正在获取 {firmware_type} 固件信息...")
                app_status_label.config(text="正在获取固件信息...", foreground="blue")
                self.update_idletasks()
                
                # 使用GET请求，流式处理避免下载整个文件
                try:
                    with requests.get(final_url, stream=True, timeout=10, headers=browser_headers) as r:
                        if r.status_code != 200:
                            error_msg = f"无法访问固件，HTTP状态码: {r.status_code}"
                            status_label.config(text=error_msg)
                            app_status_label.config(text=error_msg, foreground="red")
                            print(f"GET请求失败: {url}\n状态码: {r.status_code}\n响应头: {r.headers}")
                            self.update_idletasks()
                            time.sleep(2)
                            if status_window.winfo_exists():
                                status_window.destroy()
                            return
                        
                        # 获取远程文件大小
                        remote_size = int(r.headers.get('content-length', 0))
                        if remote_size == 0:
                            error_msg = "无法获取固件大小信息"
                            status_label.config(text=error_msg)
                            app_status_label.config(text=error_msg, foreground="red")
                            self.update_idletasks()
                            time.sleep(2)
                            if status_window.winfo_exists():
                                status_window.destroy()
                            return
                        
                        # 设置进度条
                        progress['maximum'] = remote_size
                        
                        # 开始下载到临时文件
                        status_label.config(text=f"开始下载 {firmware_type} 固件...")
                        app_status_label.config(text="开始下载固件...", foreground="blue")
                        self.update_idletasks()
                        
                        # 确保临时文件目录存在
                        os.makedirs(os.path.dirname(temp_path), exist_ok=True)
                        
                        # 下载到临时文件
                        with open(temp_path, 'wb') as f:
                            downloaded = 0
                            for chunk in r.iter_content(chunk_size=8192):
                                if status_window.winfo_exists() == 0:  # 检查窗口是否被关闭
                                    app_status_label.config(text="下载已取消", foreground="orange")
                                    return
                                if chunk:
                                    f.write(chunk)
                                    downloaded += len(chunk)
                                    progress['value'] = downloaded
                                    percent = downloaded/remote_size*100
                                    status_label.config(text=f"下载 {firmware_type} 固件: {percent:.1f}%")
                                    app_status_label.config(text=f"下载进度: {percent:.1f}%", foreground="blue")
                                    self.update_idletasks()
                except requests.exceptions.RequestException as e:
                    error_msg = f"网络错误: {str(e)}"
                    status_label.config(text=error_msg)
                    app_status_label.config(text=error_msg, foreground="red")
                    print(f"请求异常: {url}\n错误: {str(e)}\n详情: {traceback.format_exc()}")
                    self.update_idletasks()
                    time.sleep(2)
                    if status_window.winfo_exists():
                        status_window.destroy()
                    return
                
                # 备份当前固件（如果存在）
                if local_exists:
                    status_label.config(text=f"备份当前 {firmware_type} 固件...")
                    app_status_label.config(text="备份当前固件...", foreground="blue")
                    self.update_idletasks()
                    
                    # 确保备份目录存在
                    os.makedirs(os.path.dirname(backup_path), exist_ok=True)
                    shutil.copy2(local_path, backup_path)
                
                # 确保目标目录存在
                os.makedirs(os.path.dirname(local_path), exist_ok=True)
                
                # 替换为新固件
                status_label.config(text=f"安装新 {firmware_type} 固件...")
                app_status_label.config(text="安装新固件...", foreground="blue")
                self.update_idletasks()
                shutil.move(temp_path, local_path)
                
                status_label.config(text=f"{firmware_type} 固件更新成功")
                app_status_label.config(text="固件更新成功！", foreground="green")
                self.update_idletasks()
                
            except Exception as e:
                error_msg = f"更新 {firmware_type} 固件时出错: {str(e)}"
                status_label.config(text=error_msg)
                app_status_label.config(text=error_msg, foreground="red")
                print(f"一般异常:\n错误: {str(e)}\n详情: {traceback.format_exc()}")
                self.update_idletasks()
                time.sleep(2)
            
            # 完成所有更新
            progress['value'] = progress['maximum']
            self.update_idletasks()
            
            # 关闭状态窗口
            if status_window.winfo_exists():
                status_window.destroy()
                
        except Exception as e:
            if status_window.winfo_exists():
                error_msg = f"发生错误: {str(e)}"
                status_label.config(text=error_msg)
                if firmware_type == "hyplink":
                    self.gateway_firmware_status_label.config(text=error_msg, foreground="red")
                else:
                    self.sensor_firmware_status_label.config(text=error_msg, foreground="red")
                print(f"主线程异常:\n错误: {str(e)}\n详情: {traceback.format_exc()}")
                self.update_idletasks()
                time.sleep(3)
                status_window.destroy()

    def _show_about(self):
        """显示关于对话框"""
        about_window = tk.Toplevel(self)
        about_window.title("关于 FastFlasher")
        about_window.geometry("400x200")
        about_window.resizable(False, False)
        about_window.transient(self)  # 设置为主窗口的临时子窗口
        
        # 居中显示
        about_window.geometry("+%d+%d" % (
            self.winfo_rootx() + self.winfo_width() // 2 - 200,
            self.winfo_rooty() + self.winfo_height() // 2 - 100
        ))
        
        # 添加应用信息
        ttk.Label(about_window, text="FastFlasher", font=("Arial", 16, "bold")).pack(pady=(20, 5))
        ttk.Label(about_window, text="版本: 1.0").pack(pady=2)
        ttk.Label(about_window, text="© 2023-2024 Beima-Tech").pack(pady=2)
        ttk.Label(about_window, text="支持自动更新固件").pack(pady=2)
        
        # 关闭按钮
        ttk.Button(about_window, text="关闭", command=about_window.destroy).pack(pady=20)

    def _create_firmware_update_tab(self):
        parent_tab_frame = self.firmware_update_tab_frame
        
        # 添加自动更新说明
        auto_update_frame = ttk.Frame(parent_tab_frame)
        auto_update_frame.pack(fill="x", expand=False, pady=10, padx=10)
        
        ttk.Label(auto_update_frame, text="固件更新", font=("Arial", 12, "bold")).pack(pady=5)
        ttk.Label(auto_update_frame, text="点击下方按钮可以一键更新所有固件。首次更新需要输入验证密钥，之后将自动记住。\n应用启动时会自动检查并更新固件。", 
                  wraplength=550).pack(pady=5)
        
        # 创建固件状态框架
        status_frame = ttk.Frame(parent_tab_frame)
        status_frame.pack(fill="x", expand=False, pady=10, padx=10)
        
        # 网关固件状态区域
        gateway_frame = ttk.LabelFrame(status_frame, text="网关固件状态")
        gateway_frame.pack(fill="x", pady=5, padx=10, side=tk.LEFT, expand=True)
        
        self.gateway_firmware_url_var = tk.StringVar(value=self.FIRMWARE_S3_CONFIG["hyplink"]["url"])
        self.gateway_firmware_status_label = ttk.Label(gateway_frame, text="点击更新按钮更新固件")
        self.gateway_firmware_status_label.pack(pady=10)
        
        # 传感器固件状态区域
        sensor_frame = ttk.LabelFrame(status_frame, text="传感器固件状态")
        sensor_frame.pack(fill="x", pady=5, padx=10, side=tk.LEFT, expand=True)
        
        self.sensor_firmware_url_var = tk.StringVar(value=self.FIRMWARE_S3_CONFIG["hypsensor"]["url"])
        self.sensor_firmware_status_label = ttk.Label(sensor_frame, text="点击更新按钮更新固件")
        self.sensor_firmware_status_label.pack(pady=10)
        
        # 创建一个更新按钮框架，居中放置
        update_btn_frame = ttk.Frame(parent_tab_frame)
        update_btn_frame.pack(fill="x", expand=False, pady=20, padx=10)
        
        # 添加一个居中的容器
        center_frame = ttk.Frame(update_btn_frame)
        center_frame.pack(anchor="center")
        
        # 统一的更新按钮
        update_all_btn = ttk.Button(center_frame, text="一键更新所有固件", 
                                   command=self.verify_update_key_and_execute,
                                   width=20)
        update_all_btn.pack(pady=10)
        
        # 添加说明标签
        ttk.Label(update_btn_frame, text="注意: 首次更新需要输入验证密钥，之后将被记住", foreground="blue").pack(pady=5)

    def auto_update_all_firmware(self):
        """自动更新所有固件，应用启动时调用"""
        # 创建一个线程来处理所有固件的更新，以避免阻塞UI
        threading.Thread(target=self._update_all_firmware_thread, daemon=True).start()

    def _update_all_firmware_thread(self):
        """在后台线程中顺序更新所有固件"""
        try:
            # 首先更新传感器固件
            self.check_and_download_firmware("hypsensor")
            # 然后更新网关固件
            self.check_and_download_firmware("hyplink")
        except Exception as e:
            print(f"自动固件更新过程中发生错误: {e}")
            print(traceback.format_exc())

    def verify_update_key_and_execute(self):
        """验证密钥并执行固件更新"""
        # 如果密钥已验证过，直接执行更新
        if self.key_validated:
            self.auto_update_all_firmware()
            return
            
        # 创建一个对话框窗口
        key_dialog = tk.Toplevel(self)
        key_dialog.title("输入验证密钥")
        key_dialog.geometry("400x150")
        key_dialog.resizable(False, False)
        key_dialog.transient(self)  # 设置为主窗口的临时子窗口
        
        # 居中显示
        key_dialog.geometry("+%d+%d" % (
            self.winfo_rootx() + self.winfo_width() // 2 - 200,
            self.winfo_rooty() + self.winfo_height() // 2 - 75
        ))
        
        # 添加输入框和标签
        ttk.Label(key_dialog, text="请输入固件更新验证密钥:", font=("Arial", 10)).pack(pady=(20, 5))
        
        key_var = tk.StringVar()
        key_entry = ttk.Entry(key_dialog, textvariable=key_var, width=30, show="*")  # 使用*隐藏输入内容
        key_entry.pack(pady=5)
        key_entry.focus_set()  # 设置初始焦点
        
        # 状态标签用于显示错误信息
        status_label = ttk.Label(key_dialog, text="", foreground="red")
        status_label.pack(pady=5)
        
        # 验证密钥
        def validate_key():
            correct_key = "G5nXq7L2BdWzR1mVa9Ct"
            entered_key = key_var.get().strip()
            
            if not entered_key:
                status_label.config(text="请输入密钥")
                return
            
            if entered_key == correct_key:
                # 验证成功，记录验证状态并保存配置
                self.key_validated = True
                self.save_config()
                
                # 关闭对话框并启动更新
                key_dialog.destroy()
                # 启动自动更新线程
                self.auto_update_all_firmware()
            else:
                # 验证失败，显示错误信息
                status_label.config(text="密钥验证失败，请重试")
                key_entry.select_range(0, tk.END)  # 全选输入内容
                key_entry.focus_set()  # 重新获取焦点
        
        # 添加按钮框架
        button_frame = ttk.Frame(key_dialog)
        button_frame.pack(pady=10)
        
        # 确定和取消按钮
        ttk.Button(button_frame, text="确定", command=validate_key).pack(side=tk.LEFT, padx=10)
        ttk.Button(button_frame, text="取消", command=key_dialog.destroy).pack(side=tk.LEFT, padx=10)
        
        # 绑定Enter键到验证函数
        key_dialog.bind("<Return>", lambda event: validate_key())
        
        # 阻止其他操作直到对话框关闭
        key_dialog.grab_set()
        key_dialog.wait_window()


if __name__ == "__main__":
    sys_argv_original = list(sys.argv)
    multiprocessing.freeze_support()

    start_gui = True
    if len(sys_argv_original) > 1:
        if sys_argv_original[1] == "-m":
            if len(sys_argv_original) > 2:
                start_gui = False
                module_name = sys_argv_original[2]  # e.g., 'esptool'
                actual_args_for_module = sys_argv_original[3:]  # e.g., ['-p', 'COM35', ...]

                # Prepare sys.argv as the module would expect it if run directly.
                # sys.argv[0] is usually the script name/path, followed by arguments.
                # We use module_name as a placeholder for what would be sys.argv[0] for the module.
                # runpy with alter_sys=True should then replace this placeholder with the actual module path.
                prepared_argv = [module_name] + actual_args_for_module

                # Store current sys.argv (which is sys_argv_original at this point of initial launch)
                # to restore it after runpy.run_module, though runpy with alter_sys=True should handle its own context.
                # However, direct modification of sys.argv is potent, so careful restoration is good practice if needed.
                # For now, let's assume runpy will handle it or the process will exit.

                # We are modifying the global sys.argv that runpy will see and potentially alter.
                current_global_sys_argv = list(sys.argv)  # Save for restoration
                sys.argv = prepared_argv

                print(f"DEBUG: Running module {module_name}. Original sys.argv: {sys_argv_original}", file=sys.stderr)
                print(f"DEBUG: Set sys.argv for run_module to: {sys.argv}", file=sys.stderr)

                try:
                    # alter_sys=True: runpy will modify sys.argv[0] to the module's actual path
                    # and use sys.argv[1:] as arguments for the module.
                    runpy.run_module(module_name, run_name="__main__", alter_sys=True)
                except SystemExit as se:
                    # If the module calls sys.exit(), runpy re-raises it. We should let it propagate or handle.
                    # print(f"Module {module_name} exited with code: {se.code}", file=sys.stderr)
                    sys.exit(se.code)
                except Exception as e:
                    print(f"Error running module {module_name}: {e}", file=sys.stderr)
                    traceback.print_exc(file=sys.stderr)
                    sys.exit(1)
                finally:
                    sys.argv = current_global_sys_argv  # Restore global sys.argv
            else:
                pass

        elif sys_argv_original[1].endswith(".py"):
            start_gui = False
            script_path = sys_argv_original[1]
            actual_args_for_script = sys_argv_original[2:]

            print(f"DEBUG: Original sys.argv for .py: {sys_argv_original}", file=sys.stderr)
            print(f"DEBUG: script_path to run: {script_path}", file=sys.stderr)

            # Setup sys.argv for the script being run by runpy.run_path
            sys.argv = [script_path] + actual_args_for_script  # Corrected this line from previous step
            print(f"DEBUG: Modified sys.argv for run_path: {sys.argv}", file=sys.stderr)

            print(f"DEBUG: sys.path before run_path: {sys.path}", file=sys.stderr)

            try:
                runpy.run_path(script_path, run_name="__main__")
            except SystemExit as se:
                # print(f"Script {script_path} exited with code: {se.code}", file=sys.stderr)
                sys.exit(se.code)
            except Exception as e:
                print(f"Error running script {script_path}: {e}", file=sys.stderr)
                traceback.print_exc(file=sys.stderr)
                sys.exit(1)

            # This part is tricky: run_path itself doesn't guarantee sys.exit.
            # If the script run by run_path doesn't call sys.exit, execution continues here.
            # If we are in a non-GUI branch, we should probably exit.
            if not start_gui:
                sys.exit(0)  # Assuming success if script ran and didn't raise/exit with error

    if start_gui:
        app = FastFlasherApp()
        app.mainloop()

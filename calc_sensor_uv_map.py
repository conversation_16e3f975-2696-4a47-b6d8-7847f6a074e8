#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
把传感器 test.xlsx 的“实际微伏uV”映射到 MainTable.xlsx 的参考曲线（按电流插值出的目标微伏），
用【单调分段线性】构造 uv_corrected = f(uv_raw) 使偏差变小。

输出：
  * 分段线性 LUT（原始微伏uV, 目标微伏uV）
  * 评估与结果表 test_uvcal.xlsx（中文列名）
  * 控制台打印全部 LUT 点（易于拷贝）

可选：
  --lut-points  固定 LUT 结点个数（默认 10；设为 0 时使用 --tol-uv 自适应）
  --tol-uv      自适应模式下的容差（µV），默认 300
  --export-c-lut 导出 C 头文件（UV_LUT_X/Y/SIZE）

# 固定 10 个结点（默认就是 10）
python calc_sensor_uv_map.py test.xlsx

# 固定 10 个结点并导出 C 头文件
python calc_sensor_uv_map.py test.xlsx --export-c-lut uv_map.h

# 若想改为 12 个：
python calc_sensor_uv_map.py test.xlsx -n 12

# 若要恢复“按容差自适应”：
python calc_sensor_uv_map.py test.xlsx -n 0 --tol-uv 100
"""

import argparse
from pathlib import Path
import numpy as np
import pandas as pd

I_COL  = "实际电流A"
UV_COL = "实际微伏uV"

# 输出列（中文）
REF_UV_AT_I_COL      = "参考微伏uV（按电流插值）"
UV_CORRECTED_COL     = "校正后微伏uV"
DELTA_UV_BEFORE_COL  = "校正前偏差uV"
DELTA_UV_AFTER_COL   = "校正后偏差uV"


def load_two_cols_xlsx(path):
    df = pd.read_excel(path)
    df.columns = [c.strip() for c in df.columns]
    for col in (I_COL, UV_COL):
        if col not in df.columns:
            raise ValueError(f"{path} 未找到必需列：{col}")
    df = df.sort_values(I_COL).reset_index(drop=True)
    return df


def build_targets(main_df, test_df):
    """在 test 的电流点上，从 main 的参考曲线插值出目标微伏 T"""
    I_ref = main_df[I_COL].astype(float).to_numpy()
    U_ref = main_df[UV_COL].astype(float).to_numpy()
    I_test = test_df[I_COL].astype(float).to_numpy()
    T = np.interp(I_test, I_ref, U_ref)
    return T


def _prep_xy(x, y):
    """按 x 递增并去重（同 x 取 y 均值）"""
    order = np.argsort(x)
    x = x[order].astype(float)
    y = y[order].astype(float)
    uniq_x, idxs = np.unique(x, return_inverse=True)
    if len(uniq_x) != len(x):
        y_sum = np.zeros_like(uniq_x, dtype=float)
        cnt   = np.zeros_like(uniq_x, dtype=int)
        for i, j in enumerate(idxs):
            y_sum[j] += y[i]; cnt[j] += 1
        x, y = uniq_x, (y_sum / np.maximum(cnt, 1))
    return x, y


def piecewise_linear_lut_tol(x, y, tol):
    """按容差自适应选点（原实现）"""
    x, y = _prep_xy(x, y)
    n = len(x)
    if n <= 2: return x, y
    knots = [0, n-1]

    def interp_err(k):
        xi, yi = x[k], y[k]
        y_hat = np.interp(x, xi, yi)
        return y - y_hat

    while True:
        err = interp_err(knots)
        abs_err = np.abs(err)
        # 选非端点的最大误差索引
        cand = np.argsort(-abs_err)
        pick = next((i for i in cand if i not in knots), None)
        if pick is None or abs_err[pick] <= tol or len(knots) == n:
            break
        knots.append(int(pick)); knots.sort()

    xi, yi = x[knots], y[knots]
    return enforce_monotonic(xi, yi)


def piecewise_linear_lut_n(x, y, n_knots):
    """固定结点数（默认 10）：从端点起迭代加入最大残差点，直到达到 n_knots。"""
    x, y = _prep_xy(x, y)
    n = len(x)
    if n <= n_knots:  # 点太少，直接用全部
        return enforce_monotonic(x, y)
    # 保证至少 2 个点
    n_knots = max(2, int(n_knots))

    knots = [0, n-1]

    def interp_err(k):
        xi, yi = x[k], y[k]
        y_hat = np.interp(x, xi, yi)
        return y - y_hat

    while len(knots) < n_knots:
        err = interp_err(knots)
        abs_err = np.abs(err)
        cand = np.argsort(-abs_err)
        # 依次找不在 knots 的最大误差点
        pick = next((i for i in cand if i not in knots), None)
        if pick is None:
            break
        knots.append(int(pick)); knots.sort()

        if len(knots) == n:  # 所有点已加入
            break

    xi, yi = x[knots], y[knots]
    return enforce_monotonic(xi, yi)


def enforce_monotonic(xi, yi):
    """确保 (xi, yi) 单调非降；若出现非增斜率，移除问题点（两端保留）"""
    if len(xi) <= 2: return xi, yi
    keep = [0]
    for k in range(1, len(xi)-1):
        s1 = (yi[k]   - yi[keep[-1]]) / max(xi[k]   - xi[keep[-1]], 1e-9)
        s2 = (yi[k+1] - yi[k])        / max(xi[k+1] - xi[k],        1e-9)
        if s1 > 0 and s2 > 0:
            keep.append(k)
    keep.append(len(xi)-1)
    keep = sorted(set(keep))
    return xi[keep], yi[keep]


def export_c_lut(path, xi, yi):
    with open(path, "w", encoding="utf-8") as f:
        f.write("/* 自动生成：微伏校正 LUT */\n")
        f.write("#pragma once\n#include <stdint.h>\n\n")
        f.write(f"static const uint32_t UV_LUT_X[{len(xi)}] = {{")
        f.write(",".join(str(int(round(v))) for v in xi))
        f.write("};\n")
        f.write(f"static const uint32_t UV_LUT_Y[{len(yi)}] = {{")
        f.write(",".join(str(int(round(v))) for v in yi))
        f.write("};\n")
        f.write(f"static const uint32_t UV_LUT_SIZE = {len(xi)};\n")
        f.write("\n/* 使用方法：\n"
                " * 给定 uv_raw，按 UV_LUT_X/UV_LUT_Y 做线性插值得到 uv_corr。\n"
                " */\n")


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("test_excel", help="待校准传感器数据表 test.xlsx（中文表头：实际电流A | 实际微伏uV）")
    ap.add_argument("--main", default="MainTable.xlsx",
                    help="参考表 MainTable.xlsx（中文表头：实际电流A | 实际微伏uV）")
    ap.add_argument("-n", "--lut-points", type=int, default=15,
                    help="固定 LUT 结点数（默认 10；设 0 使用 --tol-uv 自适应）")
    ap.add_argument("--tol-uv", type=float, default=1000.0,
                    help="自适应模式容差（µV），仅当 --lut-points=0 时生效")
    ap.add_argument("--export-c-lut", help="导出 C 头文件路径（可选）")
    args = ap.parse_args()

    main_df = load_two_cols_xlsx(args.main)
    test_df = load_two_cols_xlsx(args.test_excel)

    # 目标微伏（参考表插值）
    target_uv = build_targets(main_df, test_df)

    # 构造分段线性 LUT: (uv_raw -> uv_target)
    uv_raw = test_df[UV_COL].astype(float).to_numpy()

    if args.lut_points and args.lut_points > 0:
        xi, yi = piecewise_linear_lut_n(uv_raw, target_uv, args.lut_points)
        mode_desc = f"固定结点数 = {len(xi)}"
    else:
        xi, yi = piecewise_linear_lut_tol(uv_raw, target_uv, tol=args.tol_uv)
        mode_desc = f"自适应容差 = {args.tol_uv:.0f} µV"

    # 评估与导出
    out_df = test_df.copy()
    out_df[REF_UV_AT_I_COL]     = target_uv
    out_df[UV_CORRECTED_COL]    = np.interp(out_df[UV_COL].astype(float).to_numpy(), xi, yi)
    out_df[DELTA_UV_BEFORE_COL] = out_df[UV_COL] - out_df[REF_UV_AT_I_COL]
    out_df[DELTA_UV_AFTER_COL]  = out_df[UV_CORRECTED_COL] - out_df[REF_UV_AT_I_COL]

    mean_abs_before = float(np.abs(out_df[DELTA_UV_BEFORE_COL]).mean())
    mean_abs_after  = float(np.abs(out_df[DELTA_UV_AFTER_COL]).mean())
    max_abs_after   = float(np.abs(out_df[DELTA_UV_AFTER_COL]).max())

    # 控制台输出
    xi_i = [int(round(v)) for v in xi]
    yi_i = [int(round(v)) for v in yi]
    print(f"LUT 模式：{mode_desc}")
    print(f"LUT 节点数 = {len(xi_i)}")
    print("-" * 40)
    print("对齐到 MainTable 的评估（单位：µV）")
    print(f"平均绝对偏差(校正前) = {mean_abs_before:.1f} µV")
    print(f"平均绝对偏差(校正后) = {mean_abs_after:.1f} µV")
    print(f"最大绝对偏差(校正后) = {max_abs_after:.1f} µV")
    print("-" * 40)
    print("LUT_X = [", ", ".join(map(str, xi_i)), "]", sep="")
    print("LUT_Y = [", ", ".join(map(str, yi_i)), "]", sep="")

    # 写 Excel（Sheet1 数据，Sheet2 LUT）
    out_path = Path(args.test_excel).with_stem(Path(args.test_excel).stem + "_uvcal")
    with pd.ExcelWriter(out_path, engine="openpyxl") as writer:
        cols = [I_COL, UV_COL, REF_UV_AT_I_COL, UV_CORRECTED_COL,
                DELTA_UV_BEFORE_COL, DELTA_UV_AFTER_COL]
        out_df[cols].to_excel(writer, index=False, sheet_name="校正结果")
        pd.DataFrame({"原始微伏uV": xi_i, "目标微伏uV": yi_i}).to_excel(writer, index=False, sheet_name="LUT")

    print(f"结果已写入: {out_path}")

    if args.export_c_lut:
        export_c_lut(args.export_c_lut, xi, yi)
        print(f"C 头文件已导出: {args.export_c_lut}")


if __name__ == "__main__":
    main()

import random
import string
from datetime import datetime

# Set to store used codes
used_codes = set()


# Encode number to base-36 (A-Z0-9)
def to_base36(n):
    chars = string.ascii_uppercase + string.digits
    if n == 0:
        return chars[0]
    result = ''
    while n:
        result = chars[n % 36] + result
        n //= 36
    return result.rjust(3, '0')  # Pad to 3 chars


# Generate unique 8-character code
def generate_unique_code():
    chars = string.ascii_uppercase + string.digits
    while True:
        # Get date components
        now = datetime.now()
        year = now.year - 2000  # 2025-2099 -> 25-99
        month = now.month  # 1-12
        day = now.day  # 1-31

        # Encode date: ((year * 12 + month) * 31 + day)
        # date_value = ((year * 12 + month) * 31 + day)
        date_value = ((year * 12 + (month - 1)) * 31 + (day - 1))
        prefix = to_base36(date_value)  # 3 chars

        # Generate random 5-char suffix
        suffix = ''.join(random.choice(chars) for _ in range(5))
        code = prefix + suffix

        # Check uniqueness
        if code not in used_codes:
            used_codes.add(code)
            return code

def from_base36(s):
    chars = string.ascii_uppercase + string.digits
    value = 0
    for c in s:
        value = value * 36 + chars.index(c)
    return value

def decode_date(prefix):
    date_value = from_base36(prefix)
    day = date_value % 31 + 1
    temp = date_value // 31
    month = temp % 12 + 1
    year = temp // 12 + 2000
    return year, month, day

# Example: Generate 5 codes
for _ in range(5):
    code = generate_unique_code()
    prefix = code[:3]
    decoded = decode_date(prefix)
    print(f"Code: {code}, Decoded Date: {decoded}")
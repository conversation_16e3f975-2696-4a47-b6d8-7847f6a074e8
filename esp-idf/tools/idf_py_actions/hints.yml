#  -
#     re: Regular expression of error to search
#     hint: The message of the hint. Optionally, it is possible to use '{}' at the place where the matched group from 're' should be inserted. This requires 'match_to_output: True'. You can use variables with hint messages. For this, you need to add variables and "{}" in a place where you want to put your hint variable, but you can't use 'match_to_output' with variables.
#     match_to_output: (False by default) see the description of 'hint'.
#     variables:
#       -
#           re_variables: [set variable for regular expression]
#           hint_variables: [set variable for hint]
# Rules to write regex for hints on how to resolve errors
# - Do not use more than one whitespace in a row. The script automatically merges several whitespaces into one when capturing output
# - Do not use \n in your regex. They are all automatically deletes by the script when capturing output
#
# example of using hints:
#    -
#        re: "Error: header {} is missing" (you can use '{1} ... {1}' placeholders in 'hint' and 're', so that you don't have to repeat the same variables, you can use 'hint: 'The {0} (functions/types/macros prefixed with '{1}') has been made into a private API. If users still require usage of the {0} (though this is not recommended), it can be included via  #include "esp_private/{2}.h".' in this file as an example)
#        hint: "header {} is missing, you need to add dependency on component {}"
#        variables:
#            -
#               re_variables: [Q]
#               hint_variables: [A, B]
#            -
#               re_variables: [W]
#               hint_variables: [C, D]
#            -
#               re_variables: [R]
#               hint_variables: [E, F]
#
#   that example will replace this :
#   -
#       re: "Error: header Q is missing"
#       hint: "header A is missing, you need to add dependency on component B"
#   -
#       re: Error: header W is missing"
#       hint: "header C is missing, you need to add dependency on component D"
#   -
#       re: Error: header R is missing"
#       hint: "header E is missing, you need to add dependency on component F"

-
    re: "warning: passing argument 1 of 'esp_secure_boot_read_key_digests' from incompatible pointer type"
    hint: "The parameter type of the function esp_secure_boot_read_key_digests() has been changed from ets_secure_boot_key_digests_t* to esp_secure_boot_key_digests_t*."

-
    re: "error: implicit declaration of function '{}'"
    hint: "Function '{}' has been removed. Please use the function {}."
    variables:
        -
            re_variables: ['bootloader_common_get_reset_reason']
            hint_variables: ['bootloader_common_get_reset_reason()', "'esp_rom_get_reset_reason()' in the ROM component"]
        -
            re_variables: ['esp_efuse_get_chip_ver']
            hint_variables: ['esp_efuse_get_chip_ver()', 'efuse_hal_get_major_chip_version()', 'efuse_hal_get_minor_chip_version() or efuse_hal_chip_revision() instead']
        -
            re_variables: ['(esp_spiram_get_chip_size|esp_spiram_get_size)']
            hint_variables: ['esp_spiram_get_chip_size and esp_spiram_get_size', 'esp_psram_get_size()']

-
    re: "error: implicit declaration of function 'esp_secure_boot_verify_rsa_signature_block'"
    hint: "'esp_secure_boot_verify_rsa_signature_block()' has been made private and is no longer available."

-
    re: "error: implicit declaration of function '{}'"
    hint: '{0}.h header file is not included by esp_system.h anymore. It shall then be manually included with #include "{0}.h"'
    variables:
        -
            re_variables: ['(esp_random|esp_fill_random)']
            hint_variables: ['esp_random']
        -
            re_variables: ['(esp_base_mac_addr_(s|g)et|esp_efuse_mac_get_(custom|default)|esp_read_mac|esp_derive_local_mac)']
            hint_variables: ['esp_mac']
        -
            re_variables: ['esp_chip_info']
            hint_variables: ['esp_chip_info']

-
    re: "fatal error: (spiram.h|esp_spiram.h): No such file or directory"
    hint: "{} was removed. Include esp_psram.h instead. Make sure to also add esp_psram as a dependency in your CMakeLists.txt file."
    match_to_output: True

-
    re: "error: implicit declaration of function '{}'"
    hint: "Use {} defined in esp_cpu.h instead of {}."
    variables:
        -
            re_variables: ['esp_cpu_ccount_t']
            hint_variables: ['esp_cpu_cycle_count_t', 'esp_cpu_ccount_t']
        -
            re_variables: ['esp_cpu_get_ccount']
            hint_variables: ['esp_cpu_get_cycle_count()', 'esp_cpu_get_ccount']
        -
            re_variables: ['esp_cpu_set_ccount']
            hint_variables: ['esp_cpu_set_cycle_count()', 'esp_cpu_set_ccount']

-
    re: "fatal error: {}: No such file or directory"
    hint: "{} was removed. Include {} instead."
    variables:
        -
            re_variables: ['esp_intr.h']
            hint_variables: ['esp_intr.h', 'esp_intr_alloc.h']
        -
            re_variables: ['soc/cpu.h']
            hint_variables: ['soc/cpu.h', 'and use the API function provided by esp_cpu.h']
        -
            re_variables: ['compare_set.h']
            hint_variables: ['compare_set.h', 'and use the API function provided by esp_cpu.h']
        -
            re_variables: ['esp_panic.h']
            hint_variables: ['esp_panic.h', 'use functionalities provided in esp_debug_helpers.h']

        -   re_variables: ['driver/can.h']
            hint_variables: ['driver/can.h', 'driver/twai.h']

-
    re: "error: implicit declaration of function 'esp_int_wdt_\\w+'"
    hint: 'The Interrupt Watchdog API has been made private, it shall not be used anymore. You can still force its inclusion with #include "esp_private/esp_int_wdt.h" (not recommended)'

-
    re: "fatal error: soc/(spinlock.h|clk_ctrl_os.h|rtc_wdt.h): No such file or directory"
    hint: "{} must be included without the 'soc' part."
    match_to_output: True

-
    re: "fatal error: (soc_log.h): No such file or directory"
    hint: "{} was renamed and made private. Consider using the logging APIs provided under esp_log.h instead."
    match_to_output: True

-
    re: "error: unknown type name '(portTickType|xTaskHandle|xQueueHandle|xSemaphoreHandle|xQueueSetHandle|xQueueSetMemberHandle|xTimeOutType|xMemoryRegion|xTaskParameters|xTaskStatusType|xTimerHandle|xCoRoutineHandle|pdTASK_HOOK_CODE|tmrTIMER_CALLBACK|pdTASK_CODE|xListItem|xList)'"
    hint: "You are maybe using pre FreeRTOS V8.0.0 data types. The backward compatibility of such data types is no longer enabled by default. Please turn on CONFIG_FREERTOS_ENABLE_BACKWARD_COMPATIBILITY explicitly to use such data types."
    match_to_output: True
-
    re: "error: 'portTICK_RATE_MS' undeclared"
    hint: "You are maybe using pre FreeRTOS V8.0.0 APIs. The backward compatibility of such APIs is no longer enabled by default. Please turn on CONFIG_FREERTOS_ENABLE_BACKWARD_COMPATIBILITY explicitly to use such APIs."
    match_to_output: True
-
    re: "error: implicit declaration of function '(eTaskStateGet|pcTaskGetTaskName|pcTimerGetTimerName|pcQueueGetQueueName|vTaskGetTaskInfo|xTaskGetIdleRunTimeCounter)'"
    hint: "You are maybe using pre FreeRTOS V8.0.0 APIs. The backward compatibility of such APIs is no longer enabled by default. Please turn on CONFIG_FREERTOS_ENABLE_BACKWARD_COMPATIBILITY explicitly to use such APIs."
    match_to_output: True
-
    re: "error: implicit declaration of function '(portENTER_CRITICAL_NESTED|portEXIT_CRITICAL_NESTED|vPortCPUInitializeMutex|vPortCPUAcquireMutex|vPortCPUAcquireMutexTimeout|vPortCPUReleaseMutex)'"
    hint: "The header file portmacro_deprecated.h has been removed. Users should refer the migration guide for alternative functions."
    match_to_output: True

-
    re: "fatal error: {}.h: No such file or directory"
    hint: 'The {0} (functions/types/macros prefixed with "{1}") has been made into a private API. If users still require usage of the {0} (though this is not recommended), it can be included via  #include "esp_private/{2}.h".'
    variables:
        -
            re_variables: ['esp32\\w*\\/clk']
            hint_variables: ['ESP Clock API', 'esp_clk', 'esp_clk']
        -
            re_variables: ['esp32\\w*\\/cache_err_int']
            hint_variables: ['Cache Error Interrupt API', 'esp_cache_err', 'cache_err_int']
        -
            re_variables: ['brownout']
            hint_variables: ['Brownout API', 'esp_brownout', 'brownout']
        -
            re_variables: ['trax']
            hint_variables: ['Trax API', 'trax_', 'trax']
        -
            re_variables: ['eh_frame_parser']
            hint_variables: ['Backtrace Parser API', 'eh_frame_parser', 'eh_frame_parser']

-
    re: "fatal error: esp_adc_cal.h: No such file or directory"
    hint: "``esp_adc_cal`` component is no longer supported. New adc calibration driver is in ``esp_adc``. Legacy adc calibration driver has been moved into ``esp_adc`` component. To use legacy ``esp_adc_cal`` driver APIs, you should add ``esp_adc`` component to the list of component requirements in CMakeLists.txt. For more information run 'idf.py docs -sp migration-guides/release-5.x/peripherals.html'."

-
    re: "fatal error: .*atca_mbedtls_wrap\\.h: No such file or directory"
    hint: "To use CONFIG_ESP_TLS_USE_SECURE_ELEMENT option, please install `esp-cryptoauthlib` using 'idf.py add-dependency espressif/esp-cryptoauthlib'"

-
    re: "The CMAKE_[A-Z]+_COMPILER: [\\w+-]+ is not a full path and was not found in the PATH\\."
    hint: "Try to reinstall the toolchain for the chip that you trying to use. \nFor more information run 'idf.py docs -sp get-started/#installation' and follow the instructions for your system"

-
    re: "CMake Error: The current CMakeCache\\.txt directory .* is different than the directory .* where CMakeCache\\.txt was created\\."
    hint: "Run 'idf.py fullclean' and try the build again."

-
    re: "CMake Error at .* \\(message\\): Could not create symbolic link for: error\\.c --> Cannot create a file when that file already exists\\."
    hint: "Run 'idf.py fullclean' and try the build again."

-
    re: "CMake Error at .* \\(message\\): Directory specified in EXTRA_COMPONENT_DIRS doesn't exist: \\/?.*\\/examples\\/common_components\\/.*"
    hint: "The component with path specified in the EXTRA_COMPONENT_DIRS variable has been moved to IDF component manager (or has been removed).\nPlease look out for component in 'https://components.espressif.com' and add using 'idf.py add-dependency' command.\nRefer to the migration guide for more details."

-
    re: "ImportError: bad magic number in 'kconfiglib':"
    hint: "Run 'idf.py python-clean', and try again"

-
    re: "ccache error: Failed to create temporary file"
    hint: "On Windows, you should enable long path support in the installer, or disable ccache temporarily. See 'idf.py --help' or the documentation how to achieve this."

-
    re: "The keyword signature for target_link_libraries has already been used"
    hint: "Projects using target_link_libraries with project_elf explicitly and custom CMake projects must specify PRIVATE, PUBLIC or INTERFACE arguments."

-
    re: "format '([^']+)' expects argument of type '((unsigned )?int|long)', but argument (\\w+) has type '([u]?int32_t)'( \\{aka '([^']+)'\\})?"
    hint: "The issue is better to resolve by replacing format specifiers to 'PRI'-family macros (include <inttypes.h> header file)."

-
    re: "Failed to resolve component 'esp_ipc'"
    hint: "IPC component has been moved to esp_system. Any `REQUIRES esp_ipc` can simply be deleted as esp_system is REQUIRED by default."

-
    re: "error: invalid use of incomplete typedef 'esp_tls_t'"
    hint: "The struct 'esp_tls_t' has now been made private - its elements can be only be accessed/modified through respective getter/setter functions. Please refer to the migration guide for more information."

-
    re: "error: enumeration value 'HTTP_EVENT_REDIRECT' not handled in switch"
    hint: "The event handler, specified in the 'event_handler' element, of the 'esp_http_client_config_t' struct now needs to handle the new 'HTTP_EVENT_REDIRECT' event case."

-
    re: "Failed to resolve component '(?!esp_ipc)(\\w+)'"
    hint: "The component {} could not be found. This could be because: component name was misspelled, the component was not added to the build, the component has been moved to the IDF component manager or has been removed and refactored into some other component.\nPlease look out for component in 'https://components.espressif.com' and add using 'idf.py add-dependency' command.\nRefer to the migration guide for more details about moved components.\nRefer to the build-system guide for more details about how components are found and included in the build."
    match_to_output: True

-
    re: "fatal error: (esp_rom_tjpgd.h): No such file or directory"
    hint: "{} was removed. Please use esp_jpeg component from IDF component manager instead.\nPlease look out for component in 'https://components.espressif.com' and add using 'idf.py add-dependency' command.\nRefer to the migration guide for more details."
    match_to_output: True

-
    re: "(fatal error: tcpip_adapter.h: No such file or directory|error: implicit declaration of function 'tcpip_adapter_init')"
    hint: "TCP/IP adapter compatibility layer has been removed. Please migrate to ESP-NETIF.\nRefer to the Networking migration guide, section TCP/IP adapter, for more details."

-
    re: "error: macro \"(ETH_\\w+_CONFIG)\" requires 2 arguments, but only 1 given"
    hint: "Macro {} now accepts both SPI host and SPI device related configuration. The SPI-Ethernet Module initialization has been simplified to allocate an SPI device internally so the configuration structure requires the related configuration.\nPlease refer to the Networking migration guide, section SPI-Ethernet Module Initialization, for more details."
    match_to_output: True

-
    re: "error: implicit declaration of function '(esp_eth_detect_phy_addr)'"
    hint: "Function {}() has been renamed to esp_eth_phy_802_3_detect_phy_addr().\nPlease refer to the Networking migration guide, section PHY Address Auto-detect, for more details."
    match_to_output: True

-
    re: "error: too few arguments to function '(esp_eth_mac_\\w+)'"
    hint: "Function {}() has been refactored to accept device specific configuration and MAC specific configuration.\nPlease refer to the Ethernet section of Networking migration guide for more details."
    match_to_output: True

-
    re: "error: implicit declaration of function 'esp_eth_phy_new_{}'"
    hint: "Function {}() has been removed, please use {}() instead.\nPlease refer to the Networking migration guide, section PHY Address Auto-detect, for more details."
    variables:
        -
            re_variables: ['ksz8081']
            hint_variables: ['esp_eth_phy_new_ksz8081', 'esp_eth_phy_new_ksz80xx']
        -
            re_variables: ['ksz8041']
            hint_variables: ['esp_eth_phy_new_ksz8041', 'esp_eth_phy_new_ksz80xx']
        -
            re_variables: ['lan8720']
            hint_variables: ['esp_eth_phy_new_lan8720', 'esp_eth_phy_new_lan87xx']

-
    re: "`iram0_0_seg' overflowed"
    hint: "The applications static IRAM usage is larger than the available IRAM size.\nFor more information on how to reduze IRAM usage run 'idf.py docs -sp api-guides/performance/ram-usage.html#optimizing-iram-usage' "

-
    re: "fatal error: (tinyusb.h): No such file or directory"
    hint: "{} was removed. Please use esp_tinyusb component from IDF component manager instead.\nYou can install `esp_tinyusb` using 'idf.py add-dependency espressif/esp_tinyusb' command.\nRefer to the migration guide for more details."
    match_to_output: True

-
    re: "fatal error: esp_partition.h: No such file or directory"
    hint: "All the Partition APIs have been moved to the new component 'esp_partition' - please, update your project dependencies. See Storage migration guide 5.x for more details."
    match_to_output: True

-
    re: "warning: 'esp_vfs_fat_sdmmc_unmount' is deprecated: Please use esp_vfs_fat_sdcard_unmount instead [-Wdeprecated-declarations]"
    hint: "``esp_vfs_fat_sdmmc_unmount()`` is now deprecated, you can use :cpp:func:`esp_vfs_fat_sdcard_unmount()` instead. See Storage migration guide 5.1 for more details"

-
    re: "vfs_fat_sdmmc: sdmmc_card_init failed"
    hint: "Please verify if there is an SD card inserted into the SD slot. Then, try rebooting the board."

-
    re: "sdmmc_common: sdmmc_init_ocr: send_op_cond"
    hint: "Please reboot the board and then try again"

-
    re: "sdmmc_io: sdmmc_io_read_byte: sdmmc_io_rw_direct"
    hint: "Please verify that card supports IO capabilities. Refer 'IDF_PATH/examples/peripherals/sdio/host/README.md' for more details"

-
    re: "example: Failed to initialize the card \\({}\\). Make sure SD card lines have pull-up resistors in place."
    hint: "Please refer ./README.md for details"
    variables:
        -
            re_variables: ['ESP_ERR_TIMEOUT']
            hint_variables: []
        -
            re_variables: ['ESP_ERR_INVALID_RESPONSE']
            hint_variables: []
        -
            re_variables: ['ESP_ERR_INVALID_STATE']
            hint_variables: []
        -
            re_variables: ['ESP_ERR_INVALID_ARG']
            hint_variables: []

-
    re: "esp_usb_jtag: could not find or open device!"
    hint: "Please check the wire connection to debugging device or access rights to a serial port."

-
    re: "Error: couldn't bind [^:]+: Address already in use"
    hint: "Please check if another process uses the mentioned ports. OpenOCD already running, perhaps in the background?\nPlease list all processes to check if OpenOCD is already running; if so, terminate it before starting OpenOCD from idf.py"

-
    re: "Error: libusb_open\\(\\) failed with LIBUSB_ERROR_ACCESS"
    hint: "OpenOCD process does not have permissions to access the USB JTAG/serial device. Please use 'LIBUSB_DEBUG=1 idf.py openocd' to find out the device name and check its access rights."

-
    re: "Error: libusb_open\\(\\) failed with LIBUSB_ERROR_NOT_FOUND"
    hint: "Device drivers are not correct.\nPlease check configuration of USB drivers: https://docs.espressif.com/projects/esp-idf/en/latest/esp32/api-guides/jtag-debugging/configure-ft2232h-jtag.html?highlight=zadig#configure-usb-drivers"

-
    re: "(-Werror=address|-Werror=use-after-free)"
    hint: "The warning(s) '{}' may appear after compiler update above GCC-12\nTo suppress these warnings use 'idf.py menuconfig' to enable configure option 'Compiler options' -> 'Disable new warnings introduced in GCC 12'\nPlease note that this is not a permanent solution, and this option will be removed in a future update of the ESP-IDF.\nIt is strongly recommended to fix all warnings, as they may indicate potential issues!"
    match_to_output: True

-
    re: "error: implicit declaration of function '{}'"
    hint: "The HFP AG API function '{}' has been renamed to '{}'."
    variables:
        -
            re_variables: ['esp_bt_hf_init']
            hint_variables: ['esp_bt_hf_init', 'esp_hf_ag_init']
        -
            re_variables: ['esp_bt_hf_deinit']
            hint_variables: ['esp_bt_hf_deinit', 'esp_hf_ag_deinit']
        -
            re_variables: ['esp_bt_hf_register_callback']
            hint_variables: ['esp_bt_hf_register_callback', 'esp_hf_ag_register_callback']
        -
            re_variables: ['esp_bt_hf_connect']
            hint_variables: ['esp_bt_hf_connect', 'esp_hf_ag_slc_connect']
        -
            re_variables: ['esp_bt_hf_disconnect']
            hint_variables: ['esp_bt_hf_disconnect', 'esp_hf_ag_slc_disconnect']
        -
            re_variables: ['esp_bt_hf_connect_audio']
            hint_variables: ['esp_bt_hf_connect_audio', 'esp_hf_ag_audio_connect']
        -
            re_variables: ['esp_bt_hf_disconnect_audio']
            hint_variables: ['esp_bt_hf_disconnect_audio', 'esp_hf_ag_audio_disconnect']
        -
            re_variables: ['esp_bt_hf_vra']
            hint_variables: ['esp_bt_hf_vra', 'esp_hf_ag_vra_control']
        -
            re_variables: ['esp_bt_hf_volume_control']
            hint_variables: ['esp_bt_hf_volume_control', 'esp_hf_ag_volume_control']
        -
            re_variables: ['esp_hf_unat_response']
            hint_variables: ['esp_hf_unat_response', 'esp_hf_ag_unknown_at_send']
        -
            re_variables: ['esp_bt_hf_cmee_response']
            hint_variables: ['esp_bt_hf_cmee_response', 'esp_hf_ag_cmee_send']
        -
            re_variables: ['esp_bt_hf_indchange_notification']
            hint_variables: ['esp_bt_hf_indchange_notification', 'esp_hf_ag_devices_status_indchange']
        -
            re_variables: ['esp_bt_hf_cind_response']
            hint_variables: ['esp_bt_hf_cind_response', 'esp_hf_ag_cind_response']
        -
            re_variables: ['esp_bt_hf_cops_response']
            hint_variables: ['esp_bt_hf_cops_response', 'esp_hf_ag_cops_response']
        -
            re_variables: ['esp_bt_hf_clcc_response']
            hint_variables: ['esp_bt_hf_clcc_response', 'esp_hf_ag_clcc_response']
        -
            re_variables: ['esp_bt_hf_cnum_response']
            hint_variables: ['esp_bt_hf_cnum_response', 'esp_hf_ag_cnum_response']
        -
            re_variables: ['esp_bt_hf_bsir']
            hint_variables: ['esp_bt_hf_bsir', 'esp_hf_ag_bsir']
        -
            re_variables: ['esp_bt_hf_answer_call']
            hint_variables: ['esp_bt_hf_answer_call', 'esp_hf_ag_answer_call']
        -
            re_variables: ['esp_bt_hf_reject_call']
            hint_variables: ['esp_bt_hf_reject_call', 'esp_hf_ag_reject_call']
        -
            re_variables: ['esp_bt_hf_out_call']
            hint_variables: ['esp_bt_hf_out_call', 'esp_hf_ag_out_call']
        -
            re_variables: ['esp_bt_hf_end_call']
            hint_variables: ['esp_bt_hf_end_call', 'esp_hf_ag_end_call']
        -
            re_variables: ['esp_bt_hf_register_data_callback']
            hint_variables: ['esp_bt_hf_register_data_callback', 'esp_hf_ag_register_data_callback']
        -
            re_variables: ['esp_hf_outgoing_data_ready']
            hint_variables: ['esp_hf_outgoing_data_ready', 'esp_hf_ag_outgoing_data_ready']

-
    re: "error: '{}' undeclared"
    hint: "The union element '{}' in 'esp_hf_cme_err_t' has been renamed to '{}'"
    variables:
        -
            re_variables: ['ESP_HF_CME_MEMEORY_FULL']
            hint_variables: ['ESP_HF_CME_MEMEORY_FULL', 'ESP_HF_CME_MEMORY_FULL']
        -
            re_variables: ['ESP_HF_CME_MEMEORY_FAILURE']
            hint_variables: ['ESP_HF_CME_MEMEORY_FAILURE', 'ESP_HF_CME_MEMORY_FAILURE ']

-
    re: "intr_alloc: No free interrupt inputs for [_\\w]+ interrupt"
    hint: "For troubleshooting instructions related to interrupt allocation, run 'idf.py docs -sp api-reference/system/intr_alloc.html'"

-
    re: "assert failed: [\\w]+ tlsf.c:[\\d]+"
    hint: "CORRUPT HEAP: heap metadata corrupted resulting in TLSF malfunction.\nMake sure you are not making out of bound writing on the memory you allocate in your application.\nMake sure you are not writing on freed memory.\nFor more information run 'idf.py docs -sp api-reference/system/heap_debug.html'."

-
    re: "-Werror=(xor-used-as-pow|enum-int-mismatch|self-move|dangling-reference)"
    hint: "The warning(s) '{}' may appear after compiler update above GCC-13\nTo suppress these warnings use 'idf.py menuconfig' to enable configure option 'Compiler options' -> 'Disable new warnings introduced in GCC 13'\nPlease note that this is not a permanent solution, and this option will be removed in a future update of the ESP-IDF.\nIt is strongly recommended to fix all warnings, as they may indicate potential issues!"
    match_to_output: True

-
    re: "implicit declaration of function '(opendir|readdir|telldir|seekdir|rewinddir|closedir|readdir_r|scandir|alphasort)'"
    hint: "Please include <dirent.h> (not <sys/dirent.h>)"

-
    re: "unplaced orphan section"
    hint: "Avoid creating custom sections. Please refer to the 'Linker Script Generation' article in the IDF documentation to address this. Or set option CONFIG_COMPILER_ORPHAN_SECTIONS_PLACE (not recommended)."

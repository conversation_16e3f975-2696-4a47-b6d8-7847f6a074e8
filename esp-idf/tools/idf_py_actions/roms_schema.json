{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "patternProperties": {"^esp32.*$": {"anyOf": [{"type": "array", "items": {"type": "object", "properties": {"rev": {"type": "integer", "minimum": 0, "description": "Chip revision/ROM revision number"}, "build_date_str_addr": {"type": "string", "description": "The ROM build date string address to compare between ROM elf file and chip ROM memory", "pattern": "^0x[0-9a-fA-F]{8}$"}}, "required": ["rev", "build_date_str_addr"]}}]}}}
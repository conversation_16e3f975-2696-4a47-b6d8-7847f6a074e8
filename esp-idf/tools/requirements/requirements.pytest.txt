# Python package requirements for pytest in ESP-IDF.
# This feature can be enabled by running "install.{sh,bat,ps1,fish} --enable-pytest"

pytest-embedded-serial-esp
pytest-embedded-idf
pytest-embedded-jtag
pytest-embedded-qemu
pytest-rerunfailures
pytest-timeout
pytest-ignore-test-results

# build
python-gitlab
idf-build-apps

# dependencies in pytest test scripts
scapy
websocket-client
netifaces
rangehttpserver
dbus-python; sys_platform == 'linux'
protobuf
bleak
paho-mqtt
paramiko
netmiko

# iperf_test_util
pyecharts

# for twai tests, communicate with socket can device (e.g. Canable)
python-can

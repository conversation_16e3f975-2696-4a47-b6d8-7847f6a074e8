menu "Test Configuration"

    choice TEST_I2C_WORK_MODE
        prompt "I2C working mode select"
        default TEST_I2C_MASTER_MODE

        config TEST_I2C_MASTER_MODE
            bool "i2c master mode"
        config TEST_I2C_SLAVE_MODE
            bool "I2C slave mode"
    endchoice

    menu "I2C Configuration"

        orsource "$IDF_PATH/examples/common_components/env_caps/$IDF_TARGET/Kconfig.env_caps"

        config TEST_I2C_SCL_NUM
            int "SCL GPIO Num"
            range ENV_GPIO_RANGE_MIN ENV_GPIO_OUT_RANGE_MAX
            default 2 if  IDF_TARGET_ESP32S3
            default 19 if IDF_TARGET_ESP32 || IDF_TARGET_ESP32S2
            default 9
            help
                GPIO number for I2C Master clock line.

        config TEST_I2C_SDA_NUM
            int "SDA GPIO Num"
            range ENV_GPIO_RANGE_MIN ENV_GPIO_OUT_RANGE_MAX
            default 1 if  IDF_TARGET_ESP32S3
            default 18 if IDF_TARGET_ESP32 || IDF_TARGET_ESP32S2
            default 5
            help
                GPIO number for I2C Master data line.

        config TEST_I2C_PORT_NUM
            int "Port Number"
            default 0
            help
                Port number for I2C Master device.

        config TEST_I2C_MASTER_FREQUENCY
            int "Master Frequency"
            default 400000
            depends on TEST_I2C_MASTER_MODE
            help
                I2C Speed of Master device.

        config TEST_I2C_SLAVE_ADDR
            hex "ESP Slave Address"
            default 0x58
            help
                Hardware Address of I2C Slave Port.
    endmenu

    config TEST_I2C_WIFI_AP_ENABLE
        bool "Enable WIFI AP"
        default y
        # C6 has not supported WIFI yet.
        depends on TEST_I2C_MASTER_MODE && !IDF_TARGET_ESP32C6

    menu "WiFi softAP Configuration"
        depends on TEST_I2C_WIFI_AP_ENABLE

        config TEST_WIFI_SSID
            string "WiFi SSID"
            default "myssid"
            help
                SSID (network name) for the test to connect to.

        config TEST_WIFI_PASSWORD
            string "WiFi Password"
            default "mypassword"
            help
                WiFi password (WPA or WPA2) for the test to use.
        config TEST_WIFI_CHANNEL
            int "WiFi Channel"
            range 1 13
            default 1
            help
                WiFi channel (network channel) for the test to use.

        config TEST_MAX_STA_CONN
            int "Maximal STA connections"
            default 4
            help
                Max number of the STA connects to AP.
    endmenu

endmenu

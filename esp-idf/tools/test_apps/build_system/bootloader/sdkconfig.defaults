#
# Security feature
#
# Start secure boot
#
CONFIG_SECURE_SIGNED_ON_BOOT=y
CONFIG_SECURE_SIGNED_ON_UPDATE=y
CONFIG_SECURE_SIGNED_APPS=y
CONFIG_SECURE_BOOT=y
CONFIG_SECURE_BOOT_V2_ENABLED=y
CONFIG_SECURE_BOOT_BUILD_SIGNED_BINARIES=n

#
# Start flash incryption
#
CONFIG_SECURE_FLASH_ENC_ENABLED=y
CONFIG_SECURE_FLASH_ENCRYPTION_MODE_DEVELOPMENT=y

#
# Increase partition table offset
#
CONFIG_PARTITION_TABLE_OFFSET=0xe000
CONFIG_ESPTOOLPY_FLASHSIZE_8MB=y

tools/ble/**/*
tools/catch/**/*
tools/ci/check_*.py
tools/ci/check_*.txt
tools/ci/check_*.sh
tools/ci/check_copyright_config.yaml
tools/ci/get_all_test_results.py
tools/gdb_panic_server.py
tools/check_term.py
tools/python_version_checker.py
tools/generate_debug_prefix_map.py
tools/ci/astyle-rules.yml
tools/ci/checkout_project_ref.py
tools/ci/ci_fetch_submodule.py
tools/ci/ci_get_mr_info.py
tools/ci/configure_ci_environment.sh
tools/ci/generate_rules.py
tools/ci/deploy_docs.py
tools/ci/envsubst.py
tools/ci/*exclude*.txt
tools/ci/executable-list.txt
tools/ci/fix_empty_prototypes.sh
tools/ci/get-full-sources.sh
tools/ci/idf_ci_utils.py
tools/ci/mirror-submodule-update.sh
tools/ci/multirun_with_pyenv.sh
tools/ci/mypy_ignore_list.txt
tools/ci/push_to_github.sh
tools/ci/python_packages/wifi_tools.py
tools/ci/utils.sh
tools/eclipse-code-style.xml
tools/format.sh
tools/mocks/**/*
tools/set-submodules-to-github.sh
tools/templates/sample_component/CMakeLists.txt
tools/templates/sample_component/include/main.h
tools/templates/sample_component/main.c
tools/ci/cleanup_ignore_lists.py
tools/ci/artifacts_handler.py
tools/ci/get_known_failure_cases_file.py
tools/unit-test-app/**/*
tools/ci/gitlab_yaml_linter.py
tools/ci/dynamic_pipelines/**/*
tools/ci/idf_ci/**/*
tools/ci/get_supported_examples.sh
tools/ci/python_packages/common_test_methods.py
tools/ci/python_packages/gitlab_api.py
tools/ci/python_packages/idf_http_server_test/**/*
tools/ci/python_packages/idf_iperf_test_util/**/*
tools/esp_prov/**/*
tools/ci/sort_yaml.py
tools/ci/sg_rules/*

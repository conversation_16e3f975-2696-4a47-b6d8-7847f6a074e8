components/app_update/otatool.py
components/efuse/efuse_table_gen.py
components/efuse/test_efuse_host/efuse_tests.py
components/esp_coex/test_md5/test_md5.sh
components/esp_wifi/test_md5/test_md5.sh
components/espcoredump/espcoredump.py
components/fatfs/fatfsgen.py
components/fatfs/fatfsparse.py
components/fatfs/test_fatfsgen/test_fatfsgen.py
components/fatfs/test_fatfsgen/test_fatfsparse.py
components/fatfs/test_fatfsgen/test_wl_fatfsgen.py
components/fatfs/wl_fatfsgen.py
components/heap/test_multi_heap_host/test_all_configs.sh
components/mbedtls/esp_crt_bundle/gen_crt_bundle.py
components/mbedtls/esp_crt_bundle/test_gen_crt_bundle/test_gen_crt_bundle.py
components/nvs_flash/nvs_partition_generator/nvs_partition_gen.py
components/partition_table/check_sizes.py
components/partition_table/gen_empty_partition.py
components/partition_table/gen_esp32part.py
components/partition_table/gen_extra_subtypes_inc.py
components/partition_table/parttool.py
components/partition_table/test_gen_esp32part_host/check_sizes_test.py
components/partition_table/test_gen_esp32part_host/gen_esp32part_tests.py
components/spiffs/spiffsgen.py
components/spiffs/test_spiffsgen/test_spiffsgen.py
components/ulp/esp32ulp_mapgen.py
docs/check_lang_folder_sync.sh
examples/build_system/cmake/idf_as_lib/build-esp32.sh
examples/build_system/cmake/idf_as_lib/build-esp32c2.sh
examples/build_system/cmake/idf_as_lib/build-esp32c3.sh
examples/build_system/cmake/idf_as_lib/build-esp32s2.sh
examples/build_system/cmake/idf_as_lib/build-esp32s3.sh
examples/build_system/cmake/idf_as_lib/build.sh
examples/build_system/cmake/idf_as_lib/run-esp32.sh
examples/build_system/cmake/idf_as_lib/run-esp32c2.sh
examples/build_system/cmake/idf_as_lib/run-esp32c3.sh
examples/build_system/cmake/idf_as_lib/run-esp32s2.sh
examples/build_system/cmake/idf_as_lib/run-esp32s3.sh
examples/build_system/cmake/idf_as_lib/run.sh
examples/common_components/protocol_examples_tapif_io/make_tap_netif
examples/storage/parttool/parttool_example.py
examples/storage/parttool/parttool_example.sh
examples/system/app_trace_to_plot/read_trace.py
examples/system/ota/otatool/get_running_partition.py
examples/system/ota/otatool/otatool_example.py
examples/system/ota/otatool/otatool_example.sh
install.fish
install.sh
tools/check_python_dependencies.py
tools/ci/build_template_app.sh
tools/ci/check_api_violation.sh
tools/ci/check_build_test_rules.py
tools/ci/check_callgraph.py
tools/ci/check_codeowners.py
tools/ci/check_deprecated_kconfigs.py
tools/ci/check_esp_memory_utils_headers.sh
tools/ci/check_examples_extra_component_dirs.sh
tools/ci/check_executables.py
tools/ci/check_idf_version.sh
tools/ci/check_kconfigs.py
tools/ci/check_readme_links.py
tools/ci/check_requirement_files.py
tools/ci/check_rules_components_patterns.py
tools/ci/check_soc_struct_headers.py
tools/ci/check_tools_files_patterns.py
tools/ci/check_type_comments.py
tools/ci/checkout_project_ref.py
tools/ci/cleanup_ignore_lists.py
tools/ci/deploy_docs.py
tools/ci/envsubst.py
tools/ci/fix_empty_prototypes.sh
tools/ci/generate_rules.py
tools/ci/get-full-sources.sh
tools/ci/get_supported_examples.sh
tools/ci/gitlab_yaml_linter.py
tools/ci/mirror-submodule-update.sh
tools/ci/multirun_with_pyenv.sh
tools/ci/push_to_github.sh
tools/ci/sort_yaml.py
tools/ci/test_autocomplete/test_autocomplete.py
tools/ci/test_configure_ci_environment.sh
tools/ci/test_reproducible_build.sh
tools/docker/entrypoint.sh
tools/esp_app_trace/logtrace_proc.py
tools/esp_app_trace/sysviewtrace_proc.py
tools/esp_app_trace/test/logtrace/test.sh
tools/esp_app_trace/test/sysview/test.sh
tools/format.sh
tools/gdb_panic_server.py
tools/gen_esp_err_to_name.py
tools/gen_soc_caps_kconfig/gen_soc_caps_kconfig.py
tools/gen_soc_caps_kconfig/test/test_gen_soc_caps_kconfig.py
tools/idf.py
tools/idf_monitor.py
tools/idf_size.py
tools/idf_tools.py
tools/kconfig_new/confgen.py
tools/kconfig_new/confserver.py
tools/ldgen/ldgen.py
tools/ldgen/test/test_entity.py
tools/ldgen/test/test_fragments.py
tools/ldgen/test/test_generation.py
tools/ldgen/test/test_output_commands.py
tools/mass_mfg/mfg_gen.py
tools/mkdfu.py
tools/mkuf2.py
tools/python_version_checker.py
tools/set-submodules-to-github.sh
tools/test_apps/system/no_embedded_paths/check_for_file_paths.py
tools/test_idf_py/test_hints.py
tools/test_idf_py/test_idf_py.py
tools/test_idf_py/test_idf_qemu.py
tools/test_idf_tools/test_idf_tools.py
tools/test_mkdfu/test_mkdfu.py

### General ignore list
#
components/xtensa/include/xtensa/
components/xtensa/include/
components/xtensa/esp32/include/xtensa/config/
components/xtensa/esp32s2/include/xtensa/config/
components/xtensa/esp32s3/include/xtensa/config/



components/freertos/FreeRTOS-Kernel/include/freertos/
components/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos/
components/freertos/FreeRTOS-Kernel-SMP/include/freertos/
components/freertos/FreeRTOS-Kernel-SMP/portable/xtensa/include/freertos/


components/log/include/esp_log_internal.h

components/esp_rom/include/esp32s2/rom/rsa_pss.h


# LWIP: sockets.h uses #include_next<>, which doesn't work correctly with the checker
# memp_std.h is supposed to be included multiple times with different settings
components/lwip/lwip/src/include/lwip/priv/memp_std.h
components/lwip/include/lwip/sockets.h
components/lwip/lwip/src/include/lwip/prot/nd6.h
components/lwip/lwip/src/include/netif/ppp/

components/spi_flash/include/spi_flash_chip_issi.h
components/spi_flash/include/spi_flash_chip_mxic.h
components/spi_flash/include/spi_flash_chip_gd.h
components/spi_flash/include/spi_flash_chip_winbond.h
components/spi_flash/include/spi_flash_chip_boya.h
components/spi_flash/include/spi_flash_chip_th.h
components/spi_flash/include/memspi_host_driver.h
components/spi_flash/include/spi_flash_chip_driver.h
components/spi_flash/include/spi_flash_chip_generic.h

components/bootloader_support/include/esp_app_format.h

components/wpa_supplicant/include/
components/wpa_supplicant/port/
components/wpa_supplicant/esp_supplicant/include/

components/mbedtls/mbedtls/
components/mbedtls/port/include/
components/mbedtls/port/dynamic/esp_mbedtls_dynamic_impl.h

components/esp-tls/private_include/

components/protobuf-c/
components/protocomm/proto-c/

components/fatfs/vfs/vfs_fat_internal.h
components/fatfs/src/ffconf.h

components/idf_test/include/idf_performance.h

components/json/cJSON/

components/spiffs/include/spiffs_config.h

components/unity/unity/src/unity_internals.h
components/unity/unity/extras/
components/unity/include/unity_config.h
components/unity/include/unity_test_runner.h

components/cmock/CMock/src/cmock.h
components/cmock/CMock/src/cmock_internals.h


components/openthread/openthread/

### Here are the files that do not compile for some reason
#
components/app_trace/include/esp_sysview_trace.h
components/esp_gdbstub/include/esp_gdbstub.h

components/esp_hw_support/include/esp_memprot.h
components/esp_hw_support/include/esp_private/esp_memprot_internal.h
components/esp_hw_support/include/esp_private/esp_riscv_intr.h

### Here are the files that use CONFIG_XXX values but don't include sdkconfig.h
#
components/esp_coex/include/private/esp_coexist_adapter.h
components/esp_coex/include/esp_coex_i154.h
### To be fixed: headers that rely on implicit inclusion
#
components/esp_rom/include/esp32/rom/rtc.h
components/esp_rom/include/esp32c3/rom/rtc.h
components/esp_rom/include/esp32s2/rom/rtc.h
components/esp_rom/include/esp32s3/rom/rtc.h
components/esp_rom/include/esp32c2/rom/rtc.h
# TODO: IDF-9197
components/esp_rom/include/esp32c5/beta3/esp32c5/rom/rtc.h
components/esp_rom/include/esp32c5/mp/esp32c5/rom/rtc.h
components/esp_rom/include/esp32c6/rom/rtc.h
components/esp_rom/include/esp32h2/rom/rtc.h
components/esp_rom/include/esp32p4/rom/rtc.h
components/esp_rom/include/esp32c61/rom/rtc.h
components/esp_rom/include/esp32/rom/sha.h
components/esp_rom/include/esp32/rom/secure_boot.h
components/esp_rom/include/esp32c3/rom/spi_flash.h
components/esp_rom/include/esp32s2/rom/spi_flash.h
components/esp_rom/include/esp32s2/rom/cache.h
components/esp_rom/include/esp32s2/rom/secure_boot.h
components/esp_rom/include/esp32s2/rom/opi_flash.h
components/esp_ringbuf/include/freertos/ringbuf.h
components/esp_netif/include/esp_netif_defaults.h
components/esp_netif/include/esp_netif_net_stack.h
components/esp_netif/include/esp_netif_ppp.h
components/protocomm/include/transports/protocomm_httpd.h
components/fatfs/src/diskio.h
components/fatfs/diskio/diskio_sdmmc.h
components/mbedtls/esp_crt_bundle/include/esp_crt_bundle.h
components/usb/include/esp_private/usb_phy.h
components/usb/include/usb/usb_types_stack.h

### Headers that don't compile with C++
#
components/bootloader_support/bootloader_flash/include/bootloader_flash.h
components/bootloader_support/bootloader_flash/include/bootloader_flash_priv.h
components/esp_hw_support/include/esp_private/regdma_link.h
components/lwip/include/lwip/netdb.h
components/spi_flash/include/esp_private/spi_flash_os.h

### To be fixed: files which don't compile for esp32s2 target:

components/esp_psram/include/esp32/himem.h
components/esp_rom/include/esp32/rom/ets_sys.h
components/esp_rom/include/esp32/rom/uart.h


### To be fixed: files which don't compile for esp32s3 target:


### To be fixed: files which don't compile for esp32c3 target:

components/esp_system/port/include/private/esp_private/trax.h
components/espcoredump/include/port/xtensa/esp_core_dump_summary_port.h
components/riscv/include/esp_private/panic_reason.h
components/riscv/include/riscv/interrupt.h
components/riscv/include/riscv/rvruntime-frames.h

# should be private include, but 'private_include' is a subdir of public includes
components/console/private_include/console_private.h

# Missing extern "C"
components/esp_driver_spi/include/esp_private/spi_master_internal.h

library/error\.o
/.*error\S*\.o
.*error.*\.c\.obj
.*error.*\.c
.*error.*\.cpp\.obj
.*error.*\.cxx\.obj
.*error.*\.cc\.obj
-Werror
error\.d
/.*error\S*.d
reassigning to symbol
changes choice state
tool_version_check\.cmake
CryptographyDeprecationWarning
Warning: \d+/\d+ app partitions are too small for binary
CMake Deprecation Warning at main/lib/tinyxml2/CMakeLists\.txt:11 \(cmake_policy\)
The smallest .+ partition is nearly full \(\d+% free space left\)!
warning: unknown kconfig symbol 'BT_LE_50_FEATURE_SUPPORT' assigned to*
warning: unknown kconfig symbol 'BT_LE_MAX_EXT_ADV_INSTANCES' assigned to*
warning: unknown kconfig symbol 'BTDM_CTRL_MODE_BLE_ONLY' assigned to*
warning: unknown kconfig symbol 'BTDM_CTRL_MODE_BR_EDR_ONLY' assigned to*
warning: unknown kconfig symbol 'BTDM_CTRL_MODE_BTDM' assigned to*
warning: unknown kconfig symbol 'CTRL_BTDM_MODEM_SLEEP' assigned to*
warning: unknown kconfig symbol 'ESP_DEFAULT_CPU_FREQ_MHZ_80' assigned to 'y' in .*/examples/system/deep_sleep/*
warning: unknown kconfig symbol 'ESP_DEFAULT_CPU_FREQ_MHZ_80' assigned to 'y' in .*/examples/system/light_sleep/*
warning: unknown kconfig symbol 'ESP32_REV_MIN_3' assigned to 'y' in .*/examples/system/ota/simple_ota_example/sdkconfig.ci.on_update_no_sb_rsa
warning: unknown kconfig symbol 'ESP32_REV_MIN' assigned to '3' in .*/examples/system/ota/simple_ota_example/sdkconfig.ci.on_update_no_sb_rsa
warning: unknown kconfig symbol 'ESP32H4_RTC_CLK_CAL_CYCLES' assigned to '576' in .*/examples/bluetooth/nimble/blecent/sdkconfig.defaults.esp32h4
warning: unknown kconfig symbol 'ESP32H4_RTC_CLK_SRC_EXT_CRYS' assigned to 'y' in .*/examples/bluetooth/nimble/blecent/sdkconfig.defaults.esp32h4
warning: unknown kconfig symbol 'LWIP_ETHARP_TRUST_IP_MAC' assigned to 'n' in .*/examples/wifi/iperf/*
warning: unknown kconfig symbol 'LWIP_ETHARP_TRUST_IP_MAC' assigned to 'n' in .*/idf-app-test/apps/iperf/*
warning: unknown kconfig symbol 'SPIRAM_FETCH_INSTRUCTIONS' assigned to 'y' in .*/components/spi_flash/test_apps/mspi_test/sdkconfig.ci.psram
warning: unknown kconfig symbol 'SPIRAM_IGNORE_NOTFOUND' assigned to 'y' in .*/examples/network/simple_sniffer/sdkconfig.ci.mem
warning: unknown kconfig symbol 'SPIRAM_RODATA' assigned to 'y' in .*/components/spi_flash/test_apps/mspi_test/sdkconfig.ci.psram
warning: unknown kconfig symbol 'SPIRAM' assigned to 'y' in .*/examples/network/simple_sniffer/sdkconfig.ci.mem
warning: unknown kconfig symbol 'SPIRAM' assigned to 'y' in .*/examples/protocols/http2_request/sdkconfig.ci
warning: unknown kconfig symbol 'SPIRAM' assigned to 'y' in .*/examples/protocols/https_mbedtls/sdkconfig.ci
warning: unknown kconfig symbol 'SPIRAM' assigned to 'y' in .*/examples/protocols/https_request/sdkconfig.ci
warning: unknown kconfig symbol 'SPIRAM' assigned to 'y' in .*/examples/protocols/https_request/sdkconfig.ci.ssldyn
warning: unknown kconfig symbol 'UNITY_FREERTOS_STACK_SIZE' assigned to '12288' in .*/components/bt/test_apps/sdkconfig.defaults
warning: unknown kconfig symbol 'WPA3_SAE' assigned to 'y' in .*/components/wpa_supplicant/test_apps/sdkconfig.defaults

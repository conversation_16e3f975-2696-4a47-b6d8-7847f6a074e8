#
# SDK tool configuration
#
CONFIG_SDK_TOOLPREFIX="xtensa-esp32-elf-"

#
# Bootloader config
#
CONFIG_BOOTLOADER_LOG_LEVEL_NONE=
CONFIG_BOOTLOADER_LOG_LEVEL_ERROR=
CONFIG_BOOTLOADER_LOG_LEVEL_WARN=y
CONFIG_BOOTLOADER_LOG_LEVEL_INFO=
CONFIG_BOOTLOADER_LOG_LEVEL_DEBUG=
CONFIG_BOOTLOADER_LOG_LEVEL_VERBOSE=
CONFIG_BOOTLOADER_LOG_LEVEL=2
CONFIG_BOOTLOADER_VDDSDIO_BOOST_1_8V=
CONFIG_BOOTLOADER_VDDSDIO_BOOST_1_9V=y

#
# Security features
#
CONFIG_SECURE_BOOT=
CONFIG_SECURE_FLASH_ENC_ENABLED=

#
# Serial flasher config
#
CONFIG_ESPTOOLPY_FLASHMODE_QIO=
CONFIG_ESPTOOLPY_FLASHMODE_QOUT=
CONFIG_ESPTOOLPY_FLASHMODE_DIO=y
CONFIG_ESPTOOLPY_FLASHMODE_DOUT=
CONFIG_ESPTOOLPY_FLASHMODE="dio"
CONFIG_ESPTOOLPY_FLASHFREQ_80M=
CONFIG_ESPTOOLPY_FLASHFREQ_40M=y
CONFIG_ESPTOOLPY_FLASHFREQ_26M=
CONFIG_ESPTOOLPY_FLASHFREQ_20M=
CONFIG_ESPTOOLPY_FLASHFREQ="40m"
CONFIG_ESPTOOLPY_FLASHSIZE_1MB=
CONFIG_ESPTOOLPY_FLASHSIZE_2MB=y
CONFIG_ESPTOOLPY_FLASHSIZE_4MB=
CONFIG_ESPTOOLPY_FLASHSIZE_8MB=
CONFIG_ESPTOOLPY_FLASHSIZE_16MB=
CONFIG_ESPTOOLPY_FLASHSIZE="2MB"
CONFIG_ESPTOOLPY_FLASHSIZE_DETECT=y
CONFIG_ESPTOOLPY_BEFORE_RESET=y
CONFIG_ESPTOOLPY_BEFORE_NORESET=
CONFIG_ESPTOOLPY_BEFORE="default_reset"
CONFIG_ESPTOOLPY_AFTER_RESET=y
CONFIG_ESPTOOLPY_AFTER_NORESET=
CONFIG_ESPTOOLPY_AFTER="hard_reset"
CONFIG_ESPTOOLPY_MONITOR_BAUD_9600B=
CONFIG_ESPTOOLPY_MONITOR_BAUD_57600B=
CONFIG_ESPTOOLPY_MONITOR_BAUD_115200B=y
CONFIG_ESPTOOLPY_MONITOR_BAUD_230400B=
CONFIG_ESPTOOLPY_MONITOR_BAUD_921600B=
CONFIG_ESPTOOLPY_MONITOR_BAUD_2MB=
CONFIG_ESPTOOLPY_MONITOR_BAUD_OTHER=
CONFIG_ESPTOOLPY_MONITOR_BAUD_OTHER_VAL=115200
CONFIG_ESPTOOLPY_MONITOR_BAUD=115200

#
# Partition Table
#
CONFIG_PARTITION_TABLE_SINGLE_APP=y
CONFIG_PARTITION_TABLE_TWO_OTA=
CONFIG_PARTITION_TABLE_CUSTOM=
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_FILENAME="partitions_singleapp.csv"
CONFIG_PARTITION_TABLE_MD5=y

#
# Compiler options
#
CONFIG_COMPILER_OPTIMIZATION_DEBUG=y
CONFIG_COMPILER_OPTIMIZATION_SIZE=
CONFIG_COMPILER_OPTIMIZATION_NONE=
CONFIG_COMPILER_OPTIMIZATION_PERF=
CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE=y
CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_SILENT=
CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE=
CONFIG_COMPILER_CXX_EXCEPTIONS=
CONFIG_COMPILER_STACK_CHECK_MODE_NONE=y
CONFIG_COMPILER_STACK_CHECK_MODE_NORM=
CONFIG_COMPILER_STACK_CHECK_MODE_STRONG=
CONFIG_COMPILER_STACK_CHECK_MODE_ALL=
CONFIG_COMPILER_STACK_CHECK=
CONFIG_COMPILER_WARN_WRITE_STRINGS=

#
# Component config
#

#
# Application Level Tracing
#
CONFIG_ESP32_APPTRACE_DEST_TRAX=
CONFIG_ESP32_APPTRACE_DEST_NONE=y
CONFIG_ESP32_APPTRACE_ENABLE=
CONFIG_ESP32_APPTRACE_LOCK_ENABLE=y

#
# FreeRTOS SystemView Tracing
#
CONFIG_AWS_IOT_SDK=

#
# Bluetooth
#
CONFIG_BT_ENABLED=
CONFIG_BTDM_CTRL_PINNED_TO_CORE=0
CONFIG_BTDM_RESERVE_DRAM=0

#
# ADC configuration
#
CONFIG_ADC_FORCE_XPD_FSM=
CONFIG_ADC_DISABLE_DAC=y

#
# ESP32-specific
#
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_80=
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_160=
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240=y
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ=240
CONFIG_SPIRAM=
CONFIG_ESP32_MEMMAP_TRACEMEM=
CONFIG_ESP32_MEMMAP_TRACEMEM_TWOBANKS=
CONFIG_ESP32_TRAX=
CONFIG_ESP32_TRACEMEM_RESERVE_DRAM=0x0
CONFIG_ESP_COREDUMP_ENABLE_TO_FLASH=
CONFIG_ESP_COREDUMP_ENABLE_TO_UART=
CONFIG_ESP_COREDUMP_ENABLE_TO_NONE=y
CONFIG_ESP_COREDUMP_ENABLE=
CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES_TWO=
CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES_FOUR=y
CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES=4
CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE=32
CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE=2048
CONFIG_ESP_MAIN_TASK_STACK_SIZE=4096
CONFIG_ESP_IPC_TASK_STACK_SIZE=1024
CONFIG_ESP_TIMER_TASK_STACK_SIZE=3584
CONFIG_NEWLIB_STDOUT_LINE_ENDING_CRLF=y
CONFIG_NEWLIB_STDOUT_LINE_ENDING_LF=
CONFIG_NEWLIB_STDOUT_LINE_ENDING_CR=
CONFIG_NEWLIB_STDIN_LINE_ENDING_CRLF=
CONFIG_NEWLIB_STDIN_LINE_ENDING_LF=
CONFIG_NEWLIB_STDIN_LINE_ENDING_CR=y
CONFIG_NEWLIB_NANO_FORMAT=
CONFIG_ESP_CONSOLE_UART_DEFAULT=y
CONFIG_ESP_CONSOLE_UART_CUSTOM=
CONFIG_ESP_CONSOLE_UART_NONE=
CONFIG_ESP_CONSOLE_UART_NUM=0
CONFIG_ESP_CONSOLE_UART_BAUDRATE=115200
CONFIG_ULP_COPROC_ENABLED=
CONFIG_ULP_COPROC_RESERVE_MEM=0
CONFIG_ESP_SYSTEM_PANIC_PRINT_HALT=
CONFIG_ESP_SYSTEM_PANIC_PRINT_REBOOT=y
CONFIG_ESP_SYSTEM_PANIC_SILENT_REBOOT=
CONFIG_ESP_SYSTEM_PANIC_GDBSTUB=
CONFIG_ESP_DEBUG_OCDAWARE=y
CONFIG_ESP_INT_WDT=y
CONFIG_ESP_INT_WDT_TIMEOUT_MS=300
CONFIG_ESP_TASK_WDT_EN=y
CONFIG_ESP_TASK_WDT_INIT=y
CONFIG_ESP_TASK_WDT_PANIC=
CONFIG_ESP_TASK_WDT_TIMEOUT_S=5
CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0=y
CONFIG_ESP_BROWNOUT_DET=y
CONFIG_ESP_BROWNOUT_DET_LVL_SEL_0=y
CONFIG_ESP_BROWNOUT_DET_LVL_SEL_1=
CONFIG_ESP_BROWNOUT_DET_LVL_SEL_2=
CONFIG_ESP_BROWNOUT_DET_LVL_SEL_3=
CONFIG_ESP_BROWNOUT_DET_LVL_SEL_4=
CONFIG_ESP_BROWNOUT_DET_LVL_SEL_5=
CONFIG_ESP_BROWNOUT_DET_LVL_SEL_6=
CONFIG_ESP_BROWNOUT_DET_LVL_SEL_7=
CONFIG_ESP_BROWNOUT_DET_LVL=0
CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC_HRT=y
CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC=
CONFIG_NEWLIB_TIME_SYSCALL_USE_HRT=
CONFIG_NEWLIB_TIME_SYSCALL_USE_NONE=
CONFIG_RTC_CLK_SRC_INT_RC=y
CONFIG_RTC_CLK_SRC_EXT_CRYS=
CONFIG_RTC_CLK_CAL_CYCLES=1024
CONFIG_RTC_XTAL_BOOTSTRAP_CYCLES=100
CONFIG_ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY=2000
CONFIG_XTAL_FREQ_40=y
CONFIG_XTAL_FREQ_26=
CONFIG_XTAL_FREQ_AUTO=
CONFIG_XTAL_FREQ=40
CONFIG_ESP32_DISABLE_BASIC_ROM_CONSOLE=
CONFIG_ESP_TIMER_PROFILING=
CONFIG_APP_COMPATIBLE_PRE_V2_1_BOOTLOADERS=
CONFIG_ESP_ERR_TO_NAME_LOOKUP=y

#
# Wi-Fi
#
CONFIG_ESP_WIFI_STATIC_RX_BUFFER_NUM=10
CONFIG_ESP_WIFI_DYNAMIC_RX_BUFFER_NUM=32
CONFIG_ESP_WIFI_STATIC_TX_BUFFER=
CONFIG_ESP_WIFI_DYNAMIC_TX_BUFFER=y
CONFIG_ESP_WIFI_TX_BUFFER_TYPE=1
CONFIG_ESP_WIFI_DYNAMIC_TX_BUFFER_NUM=32
CONFIG_ESP_WIFI_AMPDU_TX_ENABLED=y
CONFIG_ESP_WIFI_TX_BA_WIN=6
CONFIG_ESP_WIFI_AMPDU_RX_ENABLED=y
CONFIG_ESP_WIFI_RX_BA_WIN=6
CONFIG_ESP_WIFI_NVS_ENABLED=y

#
# PHY
#
CONFIG_ESP32_PHY_CALIBRATION_AND_DATA_STORAGE=y
CONFIG_ESP32_PHY_INIT_DATA_IN_PARTITION=
CONFIG_ESP32_PHY_MAX_WIFI_TX_POWER=20
CONFIG_ESP32_PHY_MAX_TX_POWER=20

#
# Power Management
#
CONFIG_PM_ENABLE=

#
# ADC-Calibration
#
CONFIG_ADC_CAL_EFUSE_TP_ENABLE=y
CONFIG_ADC_CAL_EFUSE_VREF_ENABLE=y
CONFIG_ADC_CAL_LUT_ENABLE=y

#
# Ethernet
#
CONFIG_ETH_DMA_RX_BUF_NUM=10
CONFIG_ETH_DMA_TX_BUF_NUM=10
CONFIG_ETH_EMAC_L2_TO_L3_RX_BUF_MODE=
CONFIG_ETH_EMAC_TASK_PRIORITY=20

#
# FAT Filesystem support
#
CONFIG_FATFS_CODEPAGE_DYNAMIC=
CONFIG_FATFS_CODEPAGE_437=y
CONFIG_FATFS_CODEPAGE_720=
CONFIG_FATFS_CODEPAGE_737=
CONFIG_FATFS_CODEPAGE_771=
CONFIG_FATFS_CODEPAGE_775=
CONFIG_FATFS_CODEPAGE_850=
CONFIG_FATFS_CODEPAGE_852=
CONFIG_FATFS_CODEPAGE_855=
CONFIG_FATFS_CODEPAGE_857=
CONFIG_FATFS_CODEPAGE_860=
CONFIG_FATFS_CODEPAGE_861=
CONFIG_FATFS_CODEPAGE_862=
CONFIG_FATFS_CODEPAGE_863=
CONFIG_FATFS_CODEPAGE_864=
CONFIG_FATFS_CODEPAGE_865=
CONFIG_FATFS_CODEPAGE_866=
CONFIG_FATFS_CODEPAGE_869=
CONFIG_FATFS_CODEPAGE_932=
CONFIG_FATFS_CODEPAGE_936=
CONFIG_FATFS_CODEPAGE_949=
CONFIG_FATFS_CODEPAGE_950=
CONFIG_FATFS_CODEPAGE=437
CONFIG_FATFS_LFN_NONE=y
CONFIG_FATFS_LFN_HEAP=
CONFIG_FATFS_LFN_STACK=
CONFIG_FATFS_FS_LOCK=0
CONFIG_FATFS_TIMEOUT_MS=10000
CONFIG_FATFS_PER_FILE_CACHE=y

#
# FreeRTOS
#
CONFIG_FREERTOS_UNICORE=y
CONFIG_FREERTOS_CORETIMER_0=y
CONFIG_FREERTOS_CORETIMER_1=
CONFIG_FREERTOS_HZ=1000
CONFIG_FREERTOS_ASSERT_ON_UNTESTED_FUNCTION=y
CONFIG_FREERTOS_CHECK_STACKOVERFLOW_NONE=
CONFIG_FREERTOS_CHECK_STACKOVERFLOW_PTRVAL=y
CONFIG_FREERTOS_CHECK_STACKOVERFLOW_CANARY=
CONFIG_FREERTOS_WATCHPOINT_END_OF_STACK=
CONFIG_FREERTOS_INTERRUPT_BACKTRACE=y
CONFIG_FREERTOS_THREAD_LOCAL_STORAGE_POINTERS=3
CONFIG_FREERTOS_ASSERT_FAIL_ABORT=y
CONFIG_FREERTOS_ASSERT_FAIL_PRINT_CONTINUE=
CONFIG_FREERTOS_ASSERT_DISABLE=
CONFIG_FREERTOS_IDLE_TASK_STACKSIZE=1024
CONFIG_FREERTOS_ISR_STACKSIZE=1536
CONFIG_FREERTOS_LEGACY_HOOKS=
CONFIG_FREERTOS_MAX_TASK_NAME_LEN=16
CONFIG_FREERTOS_SUPPORT_STATIC_ALLOCATION=
CONFIG_FREERTOS_TIMER_TASK_PRIORITY=1
CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH=2048
CONFIG_FREERTOS_TIMER_QUEUE_LENGTH=10
CONFIG_FREERTOS_QUEUE_REGISTRY_SIZE=0
CONFIG_FREERTOS_USE_TRACE_FACILITY=
CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS=
CONFIG_FREERTOS_DEBUG_INTERNALS=

#
# Heap memory debugging
#
CONFIG_HEAP_POISONING_DISABLED=y
CONFIG_HEAP_POISONING_LIGHT=
CONFIG_HEAP_POISONING_COMPREHENSIVE=
CONFIG_HEAP_TRACING=

#
# libsodium
#
CONFIG_LIBSODIUM_USE_MBEDTLS_SHA=y

#
# Log output
#
CONFIG_LOG_DEFAULT_LEVEL_NONE=
CONFIG_LOG_DEFAULT_LEVEL_ERROR=
CONFIG_LOG_DEFAULT_LEVEL_WARN=
CONFIG_LOG_DEFAULT_LEVEL_INFO=y
CONFIG_LOG_DEFAULT_LEVEL_DEBUG=
CONFIG_LOG_DEFAULT_LEVEL_VERBOSE=
CONFIG_LOG_DEFAULT_LEVEL=3
CONFIG_LOG_COLORS=y

#
# LWIP
#
CONFIG_LWIP_L2_TO_L3_COPY=
CONFIG_LWIP_IRAM_OPTIMIZATION=
CONFIG_LWIP_MAX_SOCKETS=4
CONFIG_LWIP_SO_REUSE=
CONFIG_LWIP_SO_RCVBUF=
CONFIG_LWIP_DHCP_MAX_NTP_SERVERS=1
CONFIG_LWIP_IP_FRAG=
CONFIG_LWIP_IP_REASSEMBLY=
CONFIG_LWIP_STATS=
CONFIG_LWIP_ETHARP_TRUST_IP_MAC=y
CONFIG_LWIP_TCPIP_RECVMBOX_SIZE=32
CONFIG_LWIP_DHCP_DOES_ARP_CHECK=y

#
# DHCP server
#
CONFIG_LWIP_DHCPS_LEASE_UNIT=60
CONFIG_LWIP_DHCPS_MAX_STATION_NUM=8
CONFIG_LWIP_AUTOIP=
CONFIG_LWIP_NETIF_LOOPBACK=y
CONFIG_LWIP_LOOPBACK_MAX_PBUFS=8

#
# TCP
#
CONFIG_LWIP_MAX_ACTIVE_TCP=16
CONFIG_LWIP_MAX_LISTENING_TCP=16
CONFIG_LWIP_TCP_MAXRTX=12
CONFIG_LWIP_TCP_SYNMAXRTX=6
CONFIG_LWIP_TCP_MSS=1436
CONFIG_LWIP_TCP_MSL=60000
CONFIG_LWIP_TCP_SND_BUF_DEFAULT=5744
CONFIG_LWIP_TCP_WND_DEFAULT=5744
CONFIG_LWIP_TCP_RECVMBOX_SIZE=6
CONFIG_LWIP_TCP_QUEUE_OOSEQ=y
CONFIG_LWIP_TCP_OVERSIZE_MSS=y
CONFIG_LWIP_TCP_OVERSIZE_QUARTER_MSS=
CONFIG_LWIP_TCP_OVERSIZE_DISABLE=

#
# UDP
#
CONFIG_LWIP_MAX_UDP_PCBS=16
CONFIG_LWIP_UDP_RECVMBOX_SIZE=6
CONFIG_LWIP_TCPIP_TASK_STACK_SIZE=2048
CONFIG_LWIP_PPP_SUPPORT=

#
# ICMP
#
CONFIG_LWIP_MULTICAST_PING=
CONFIG_LWIP_BROADCAST_PING=

#
# LWIP RAW API
#
CONFIG_LWIP_MAX_RAW_PCBS=16

#
# mbedTLS
#
CONFIG_MBEDTLS_SSL_MAX_CONTENT_LEN=16384
CONFIG_MBEDTLS_DEBUG=
CONFIG_MBEDTLS_HARDWARE_AES=y
CONFIG_MBEDTLS_HARDWARE_MPI=y
CONFIG_MBEDTLS_HARDWARE_SHA=
CONFIG_MBEDTLS_HAVE_TIME=y
CONFIG_MBEDTLS_HAVE_TIME_DATE=
CONFIG_MBEDTLS_TLS_SERVER_AND_CLIENT=y
CONFIG_MBEDTLS_TLS_SERVER_ONLY=
CONFIG_MBEDTLS_TLS_CLIENT_ONLY=
CONFIG_MBEDTLS_TLS_DISABLED=
CONFIG_MBEDTLS_TLS_SERVER=y
CONFIG_MBEDTLS_TLS_CLIENT=y
CONFIG_MBEDTLS_TLS_ENABLED=y

#
# TLS Key Exchange Methods
#
CONFIG_MBEDTLS_PSK_MODES=
CONFIG_MBEDTLS_KEY_EXCHANGE_RSA=y
CONFIG_MBEDTLS_KEY_EXCHANGE_DHE_RSA=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ELLIPTIC_CURVE=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ECDHE_RSA=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ECDH_RSA=y
CONFIG_MBEDTLS_SSL_RENEGOTIATION=y
CONFIG_MBEDTLS_SSL_PROTO_SSL3=
CONFIG_MBEDTLS_SSL_PROTO_TLS1=y
CONFIG_MBEDTLS_SSL_PROTO_TLS1_1=y
CONFIG_MBEDTLS_SSL_PROTO_TLS1_2=y
CONFIG_MBEDTLS_SSL_PROTO_DTLS=
CONFIG_MBEDTLS_SSL_ALPN=y
CONFIG_MBEDTLS_SSL_SESSION_TICKETS=y

#
# Symmetric Ciphers
#
CONFIG_MBEDTLS_AES_C=y
CONFIG_MBEDTLS_CAMELLIA_C=
CONFIG_MBEDTLS_DES_C=
CONFIG_MBEDTLS_RC4_DISABLED=y
CONFIG_MBEDTLS_RC4_ENABLED_NO_DEFAULT=
CONFIG_MBEDTLS_RC4_ENABLED=
CONFIG_MBEDTLS_BLOWFISH_C=
CONFIG_MBEDTLS_XTEA_C=
CONFIG_MBEDTLS_CCM_C=y
CONFIG_MBEDTLS_GCM_C=y
CONFIG_MBEDTLS_RIPEMD160_C=

#
# Certificates
#
CONFIG_MBEDTLS_PEM_PARSE_C=y
CONFIG_MBEDTLS_PEM_WRITE_C=y
CONFIG_MBEDTLS_X509_CRL_PARSE_C=y
CONFIG_MBEDTLS_X509_CSR_PARSE_C=y
CONFIG_MBEDTLS_ECP_C=y
CONFIG_MBEDTLS_ECDH_C=y
CONFIG_MBEDTLS_ECDSA_C=y
CONFIG_MBEDTLS_ECP_DP_SECP192R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_SECP224R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_SECP256R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_SECP384R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_SECP521R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_SECP192K1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_SECP224K1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_SECP256K1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_BP256R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_BP384R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_BP512R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_CURVE25519_ENABLED=y
CONFIG_MBEDTLS_ECP_NIST_OPTIM=y

#
# OpenSSL
#
CONFIG_OPENSSL_DEBUG=
CONFIG_OPENSSL_ASSERT_DO_NOTHING=y
CONFIG_OPENSSL_ASSERT_EXIT=

#
# PThreads
#
CONFIG_PTHREAD_TASK_PRIO_DEFAULT=5
CONFIG_PTHREAD_TASK_STACK_SIZE_DEFAULT=3072

#
# SPI Flash driver
#
CONFIG_SPI_FLASH_VERIFY_WRITE=
CONFIG_SPI_FLASH_ENABLE_COUNTERS=
CONFIG_SPI_FLASH_ROM_DRIVER_PATCH=y
CONFIG_SPI_FLASH_DANGEROUS_WRITE_ABORTS=y
CONFIG_SPI_FLASH_DANGEROUS_WRITE_FAILS=
CONFIG_SPI_FLASH_DANGEROUS_WRITE_ALLOWED=

#
# SPIFFS Configuration
#
CONFIG_SPIFFS_MAX_PARTITIONS=3

#
# SPIFFS Cache Configuration
#
CONFIG_SPIFFS_CACHE=y
CONFIG_SPIFFS_CACHE_WR=y
CONFIG_SPIFFS_CACHE_STATS=
CONFIG_SPIFFS_PAGE_CHECK=y
CONFIG_SPIFFS_GC_MAX_RUNS=10
CONFIG_SPIFFS_GC_STATS=
CONFIG_SPIFFS_PAGE_SIZE=256
CONFIG_SPIFFS_OBJ_NAME_LEN=32
CONFIG_SPIFFS_USE_MAGIC=y
CONFIG_SPIFFS_USE_MAGIC_LENGTH=y
CONFIG_SPIFFS_FOLLOW_SYMLINKS=
CONFIG_SPIFFS_META_LENGTH=4
CONFIG_SPIFFS_USE_MTIME=y

#
# Debug Configuration
#
CONFIG_SPIFFS_DBG=
CONFIG_SPIFFS_API_DBG=
CONFIG_SPIFFS_GC_DBG=
CONFIG_SPIFFS_CACHE_DBG=
CONFIG_SPIFFS_CHECK_DBG=
CONFIG_SPIFFS_TEST_VISUALISATION=

#
# esp_netif
#
CONFIG_NETIF_IP_LOST_TIMER_INTERVAL=120

#
# Wear Levelling
#
CONFIG_WL_SECTOR_SIZE_512=
CONFIG_WL_SECTOR_SIZE_4096=y
CONFIG_WL_SECTOR_SIZE=4096

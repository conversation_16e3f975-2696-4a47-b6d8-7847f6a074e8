In archive /home/<USER>/build/esp-idf/freertos/libfreertos.a:

FreeRTOS-openocd.c.obj:     file format elf32-xtensa-le

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .text         00000000  00000000  00000000  00000034  2**0
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  1 .data         00000000  00000000  00000000  00000034  2**0
                  CONTENTS, ALLOC, LOAD, DATA
  2 .bss          00000000  00000000  00000000  00000034  2**0
                  ALLOC
  3 .dram1        00000004  00000000  00000000  00000034  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
  4 .debug_info   0000008f  00000000  00000000  00000038  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
  5 .debug_abbrev 0000003e  00000000  00000000  000000c7  2**0
                  CONTENTS, READONLY, DEBUGGING
  6 .debug_aranges 00000018  00000000  00000000  00000105  2**0
                  CONTENTS, REL<PERSON>, READONL<PERSON>, DEBUGGING
  7 .debug_line   0000005e  00000000  00000000  0000011d  2**0
                  CONTENTS, READONLY, DEBUGGING
  8 .debug_str    0000018d  00000000  00000000  0000017b  2**0
                  CONTENTS, READONLY, DEBUGGING
  9 .comment      0000003b  00000000  00000000  00000308  2**0
                  CONTENTS, READONLY
 10 .xtensa.info  00000038  00000000  00000000  00000343  2**0
                  CONTENTS, READONLY
 11 .xt.prop      0000000c  00000000  00000000  0000037b  2**0
                  CONTENTS, RELOC, READONLY

croutine.c.obj:     file format elf32-xtensa-le

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .literal.prvCheckPendingReadyList 00000018  00000000  00000000  00000034  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  1 .literal.prvCheckDelayedList 0000002c  00000000  00000000  0000004c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  2 .literal.xCoRoutineCreate 0000001c  00000000  00000000  00000078  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  3 .literal.vCoRoutineAddToDelayedList 00000020  00000000  00000000  00000094  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  4 .literal.vCoRoutineSchedule 00000014  00000000  00000000  000000b4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  5 .literal.xCoRoutineRemoveFromEventList 00000010  00000000  00000000  000000c8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  6 .text         00000000  00000000  00000000  000000d8  2**0
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  7 .data         00000000  00000000  00000000  000000d8  2**0
                  CONTENTS, ALLOC, LOAD, DATA
  8 .bss          00000000  00000000  00000000  000000d8  2**0
                  ALLOC
  9 .text.prvCheckPendingReadyList 00000056  00000000  00000000  000000d8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 10 .text.prvCheckDelayedList 000000ac  00000000  00000000  00000130  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 11 .rodata.str1.4 00000074  00000000  00000000  000001dc  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 12 .text.xCoRoutineCreate 00000028  00000000  00000000  00000250  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 13 .text.vCoRoutineAddToDelayedList 00000056  00000000  00000000  00000278  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 14 .text.vCoRoutineSchedule 0000007a  00000000  00000000  000002d0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 15 .text.xCoRoutineRemoveFromEventList 00000031  00000000  00000000  0000034c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 16 .rodata.__FUNCTION__$5025 00000011  00000000  00000000  00000380  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 17 .bss.xPassedTicks 00000004  00000000  00000000  00000394  2**2
                  ALLOC
 18 .bss.xLastTickCount 00000004  00000000  00000000  00000394  2**2
                  ALLOC
 19 .bss.xCoRoutineTickCount 00000004  00000000  00000000  00000394  2**2
                  ALLOC
 20 .bss.uxTopCoRoutineReadyPriority 00000004  00000000  00000000  00000394  2**2
                  ALLOC
 21 .bss.pxCurrentCoRoutine 00000004  00000000  00000000  00000394  2**2
                  ALLOC
 22 .bss.xPendingReadyCoRoutineList 00000014  00000000  00000000  00000394  2**2
                  ALLOC
 23 .bss.pxOverflowDelayedCoRoutineList 00000004  00000000  00000000  00000394  2**2
                  ALLOC
 24 .bss.pxDelayedCoRoutineList 00000004  00000000  00000000  00000394  2**2
                  ALLOC
 25 .bss.pxReadyCoRoutineLists 00000028  00000000  00000000  00000394  2**2
                  ALLOC
 26 .debug_frame  000000a0  00000000  00000000  00000394  2**2
                  CONTENTS, RELOC, READONLY, DEBUGGING
 27 .debug_info   000006b8  00000000  00000000  00000434  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 28 .debug_abbrev 00000233  00000000  00000000  00000aec  2**0
                  CONTENTS, READONLY, DEBUGGING
 29 .debug_loc    0000013b  00000000  00000000  00000d1f  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 30 .debug_aranges 00000048  00000000  00000000  00000e5a  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 31 .debug_ranges 00000038  00000000  00000000  00000ea2  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 32 .debug_line   0000037e  00000000  00000000  00000eda  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 33 .debug_str    00000533  00000000  00000000  00001258  2**0
                  CONTENTS, READONLY, DEBUGGING
 34 .comment      0000003b  00000000  00000000  0000178b  2**0
                  CONTENTS, READONLY
 35 .xtensa.info  00000038  00000000  00000000  000017c6  2**0
                  CONTENTS, READONLY
 36 .xt.lit       00000030  00000000  00000000  000017fe  2**0
                  CONTENTS, RELOC, READONLY
 37 .xt.prop      00000270  00000000  00000000  0000182e  2**0
                  CONTENTS, RELOC, READONLY

event_groups.c.obj:     file format elf32-xtensa-le

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .literal.xEventGroupCreate 0000000c  00000000  00000000  00000034  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  1 .literal.xEventGroupWaitBits 0000006c  00000000  00000000  00000040  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  2 .literal.xEventGroupClearBits 00000028  00000000  00000000  000000ac  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  3 .literal.xEventGroupGetBitsFromISR 00000004  00000000  00000000  000000d4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  4 .literal.xEventGroupSetBits 0000003c  00000000  00000000  000000d8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  5 .literal.xEventGroupSync 0000005c  00000000  00000000  00000114  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  6 .literal.vEventGroupDelete 00000030  00000000  00000000  00000170  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  7 .literal.vEventGroupSetBitsCallback 00000004  00000000  00000000  000001a0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  8 .literal.vEventGroupClearBitsCallback 00000004  00000000  00000000  000001a4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  9 .text         00000000  00000000  00000000  000001a8  2**0
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
 10 .data         00000000  00000000  00000000  000001a8  2**0
                  CONTENTS, ALLOC, LOAD, DATA
 11 .bss          00000000  00000000  00000000  000001a8  2**0
                  ALLOC
 12 .text.prvTestWaitCondition 0000001e  00000000  00000000  000001a8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 13 .text.xEventGroupCreate 00000027  00000000  00000000  000001c8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 14 .rodata.str1.4 00000058  00000000  00000000  000001f0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 15 .text.xEventGroupWaitBits 00000140  00000000  00000000  00000248  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 16 .text.xEventGroupClearBits 00000060  00000000  00000000  00000388  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 17 .text.xEventGroupGetBitsFromISR 00000010  00000000  00000000  000003e8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 18 .text.xEventGroupSetBits 000000c3  00000000  00000000  000003f8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 19 .text.xEventGroupSync 00000108  00000000  00000000  000004bc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 20 .text.vEventGroupDelete 0000005d  00000000  00000000  000005c4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 21 .text.vEventGroupSetBitsCallback 0000000f  00000000  00000000  00000624  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 22 .text.vEventGroupClearBitsCallback 0000000f  00000000  00000000  00000634  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 23 .rodata.__FUNCTION__$5129 00000012  00000000  00000000  00000644  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 24 .rodata.__FUNCTION__$5120 00000013  00000000  00000000  00000658  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 25 .rodata.__FUNCTION__$5100 00000015  00000000  00000000  0000066c  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 26 .rodata.__FUNCTION__$5092 00000014  00000000  00000000  00000684  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 27 .rodata.__FUNCTION__$5078 00000010  00000000  00000000  00000698  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 28 .debug_frame  00000100  00000000  00000000  000006a8  2**2
                  CONTENTS, RELOC, READONLY, DEBUGGING
 29 .debug_info   00000e02  00000000  00000000  000007a8  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 30 .debug_abbrev 00000236  00000000  00000000  000015aa  2**0
                  CONTENTS, READONLY, DEBUGGING
 31 .debug_loc    00000546  00000000  00000000  000017e0  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 32 .debug_aranges 00000068  00000000  00000000  00001d26  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 33 .debug_ranges 00000070  00000000  00000000  00001d8e  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 34 .debug_line   000006af  00000000  00000000  00001dfe  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 35 .debug_str    00000677  00000000  00000000  000024ad  2**0
                  CONTENTS, READONLY, DEBUGGING
 36 .comment      0000003b  00000000  00000000  00002b24  2**0
                  CONTENTS, READONLY
 37 .xtensa.info  00000038  00000000  00000000  00002b5f  2**0
                  CONTENTS, READONLY
 38 .xt.lit       00000048  00000000  00000000  00002b97  2**0
                  CONTENTS, RELOC, READONLY
 39 .xt.prop      00000468  00000000  00000000  00002bdf  2**0
                  CONTENTS, RELOC, READONLY

list.c.obj:     file format elf32-xtensa-le

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .text         00000000  00000000  00000000  00000034  2**0
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  1 .data         00000000  00000000  00000000  00000034  2**0
                  CONTENTS, ALLOC, LOAD, DATA
  2 .bss          00000000  00000000  00000000  00000034  2**0
                  ALLOC
  3 .text.vListInitialise 00000015  00000000  00000000  00000034  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  4 .text.vListInitialiseItem 00000009  00000000  00000000  0000004c  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  5 .text.vListInsertEnd 0000001b  00000000  00000000  00000058  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  6 .text.vListInsert 0000002f  00000000  00000000  00000074  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  7 .text.uxListRemove 00000026  00000000  00000000  000000a4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  8 .debug_frame  00000088  00000000  00000000  000000cc  2**2
                  CONTENTS, RELOC, READONLY, DEBUGGING
  9 .debug_info   000002a1  00000000  00000000  00000154  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 10 .debug_abbrev 000000dc  00000000  00000000  000003f5  2**0
                  CONTENTS, READONLY, DEBUGGING
 11 .debug_loc    00000081  00000000  00000000  000004d1  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 12 .debug_aranges 00000040  00000000  00000000  00000552  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 13 .debug_ranges 00000030  00000000  00000000  00000592  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 14 .debug_line   0000024c  00000000  00000000  000005c2  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 15 .debug_str    000002c2  00000000  00000000  0000080e  2**0
                  CONTENTS, READONLY, DEBUGGING
 16 .comment      0000003b  00000000  00000000  00000ad0  2**0
                  CONTENTS, READONLY
 17 .xtensa.info  00000038  00000000  00000000  00000b0b  2**0
                  CONTENTS, READONLY
 18 .xt.prop      000000f0  00000000  00000000  00000b43  2**0
                  CONTENTS, RELOC, READONLY

port.c.obj:     file format elf32-xtensa-le

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .literal.pxPortInitialiseStack 00000018  00000000  00000000  00000034  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  1 .literal.xPortStartScheduler 00000014  00000000  00000000  0000004c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  2 .literal.xPortSysTickHandler 00000008  00000000  00000000  00000060  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  3 .literal.vPortYieldOtherCore 00000004  00000000  00000000  00000068  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  4 .literal.vPortReleaseTaskMPUSettings 00000004  00000000  00000000  0000006c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  5 .literal.xPortInIsrContext 00000008  00000000  00000000  00000070  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  6 .iram1.literal 00000004  00000000  00000000  00000078  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  7 .literal.vPortAssertIfInISR 00000018  00000000  00000000  0000007c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  8 .literal.vPortCPUInitializeMutex 00000004  00000000  00000000  00000094  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  9 .literal.vPortCPUAcquireMutex 00000030  00000000  00000000  00000098  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 10 .literal.vPortCPUAcquireMutexTimeout 00000030  00000000  00000000  000000c8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 11 .literal.vPortCPUReleaseMutex 00000028  00000000  00000000  000000f8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 12 .literal.vPortSetStackWatchpoint 00000008  00000000  00000000  00000120  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 13 .text         00000000  00000000  00000000  00000128  2**0
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
 14 .data         00000000  00000000  00000000  00000128  2**0
                  CONTENTS, ALLOC, LOAD, DATA
 15 .bss          00000000  00000000  00000000  00000128  2**0
                  ALLOC
 16 .text.pxPortInitialiseStack 00000086  00000000  00000000  00000128  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 17 .text.vPortEndScheduler 00000005  00000000  00000000  000001b0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
 18 .text.xPortStartScheduler 0000002e  00000000  00000000  000001b8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 19 .text.xPortSysTickHandler 00000016  00000000  00000000  000001e8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 20 .text.vPortYieldOtherCore 0000000e  00000000  00000000  00000200  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 21 .text.vPortStoreTaskMPUSettings 00000013  00000000  00000000  00000210  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
 22 .text.vPortReleaseTaskMPUSettings 0000000e  00000000  00000000  00000224  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 23 .text.xPortInIsrContext 00000026  00000000  00000000  00000234  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 24 .iram1        0000001a  00000000  00000000  0000025c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 25 .rodata.str1.4 0000013b  00000000  00000000  00000278  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 26 .text.vPortAssertIfInISR 00000025  00000000  00000000  000003b4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 27 .text.vPortCPUInitializeMutex 0000000e  00000000  00000000  000003dc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 28 .text.vPortCPUAcquireMutex 00000088  00000000  00000000  000003ec  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 29 .text.vPortCPUAcquireMutexTimeout 000000ab  00000000  00000000  00000474  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 30 .text.vPortCPUReleaseMutex 00000061  00000000  00000000  00000520  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 31 .text.vPortSetStackWatchpoint 0000001a  00000000  00000000  00000584  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 32 .text.xPortGetTickRateHz 00000008  00000000  00000000  000005a0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
 33 .rodata.__func__$5264 00000029  00000000  00000000  000005a8  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 34 .rodata.__func__$5259 00000029  00000000  00000000  000005d4  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 35 .rodata.__FUNCTION__$5243 00000013  00000000  00000000  00000600  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 36 .bss.port_interruptNesting 00000008  00000000  00000000  00000614  2**2
                  ALLOC
 37 .bss.port_xSchedulerRunning 00000008  00000000  00000000  00000614  2**2
                  ALLOC
 38 .debug_frame  00000190  00000000  00000000  00000614  2**2
                  CONTENTS, RELOC, READONLY, DEBUGGING
 39 .debug_info   00000e78  00000000  00000000  000007a4  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 40 .debug_abbrev 00000404  00000000  00000000  0000161c  2**0
                  CONTENTS, READONLY, DEBUGGING
 41 .debug_loc    000005f1  00000000  00000000  00001a20  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 42 .debug_aranges 00000098  00000000  00000000  00002011  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 43 .debug_ranges 000000a0  00000000  00000000  000020a9  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 44 .debug_line   000005fb  00000000  00000000  00002149  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 45 .debug_str    0000071f  00000000  00000000  00002744  2**0
                  CONTENTS, READONLY, DEBUGGING
 46 .comment      0000003b  00000000  00000000  00002e63  2**0
                  CONTENTS, READONLY
 47 .xtensa.info  00000038  00000000  00000000  00002e9e  2**0
                  CONTENTS, READONLY
 48 .xt.lit       00000068  00000000  00000000  00002ed6  2**0
                  CONTENTS, RELOC, READONLY
 49 .xt.prop      00000408  00000000  00000000  00002f3e  2**0
                  CONTENTS, RELOC, READONLY

port.cpp.obj:     file format elf32-xtensa-le

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .literal.pxPortInitialiseStack 00000018  00000000  00000000  00000034  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  1 .literal.xPortStartScheduler 00000014  00000000  00000000  0000004c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  2 .literal.xPortSysTickHandler 00000008  00000000  00000000  00000060  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  3 .literal.vPortYieldOtherCore 00000004  00000000  00000000  00000068  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  4 .literal.vPortReleaseTaskMPUSettings 00000004  00000000  00000000  0000006c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  5 .literal.xPortInIsrContext 00000008  00000000  00000000  00000070  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  6 .iram1.literal 00000004  00000000  00000000  00000078  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  7 .literal.vPortAssertIfInISR 00000018  00000000  00000000  0000007c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  8 .literal.vPortCPUInitializeMutex 00000004  00000000  00000000  00000094  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  9 .literal.vPortCPUAcquireMutex 00000030  00000000  00000000  00000098  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 10 .literal.vPortCPUAcquireMutexTimeout 00000030  00000000  00000000  000000c8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 11 .literal.vPortCPUReleaseMutex 00000028  00000000  00000000  000000f8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 12 .literal.vPortSetStackWatchpoint 00000008  00000000  00000000  00000120  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 13 .text         00000000  00000000  00000000  00000128  2**0
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
 14 .data         00000000  00000000  00000000  00000128  2**0
                  CONTENTS, ALLOC, LOAD, DATA
 15 .bss          00000000  00000000  00000000  00000128  2**0
                  ALLOC
 16 .text.pxPortInitialiseStack 00000086  00000000  00000000  00000128  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 17 .text.vPortEndScheduler 00000005  00000000  00000000  000001b0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
 18 .text.xPortStartScheduler 0000002e  00000000  00000000  000001b8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 19 .text.xPortSysTickHandler 00000016  00000000  00000000  000001e8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 20 .text.vPortYieldOtherCore 0000000e  00000000  00000000  00000200  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 21 .text.vPortStoreTaskMPUSettings 00000013  00000000  00000000  00000210  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
 22 .text.vPortReleaseTaskMPUSettings 0000000e  00000000  00000000  00000224  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 23 .text.xPortInIsrContext 00000026  00000000  00000000  00000234  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 24 .iram1        0000001a  00000000  00000000  0000025c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 25 .rodata.str1.4 0000013b  00000000  00000000  00000278  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 26 .text.vPortAssertIfInISR 00000025  00000000  00000000  000003b4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 27 .text.vPortCPUInitializeMutex 0000000e  00000000  00000000  000003dc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 28 .text.vPortCPUAcquireMutex 00000088  00000000  00000000  000003ec  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 29 .text.vPortCPUAcquireMutexTimeout 000000ab  00000000  00000000  00000474  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 30 .text.vPortCPUReleaseMutex 00000061  00000000  00000000  00000520  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 31 .text.vPortSetStackWatchpoint 0000001a  00000000  00000000  00000584  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 32 .text.xPortGetTickRateHz 00000008  00000000  00000000  000005a0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
 33 .rodata.__func__$5264 00000029  00000000  00000000  000005a8  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 34 .rodata.__func__$5259 00000029  00000000  00000000  000005d4  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 35 .rodata.__FUNCTION__$5243 00000013  00000000  00000000  00000600  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 36 .bss.port_interruptNesting 00000008  00000000  00000000  00000614  2**2
                  ALLOC
 37 .bss.port_xSchedulerRunning 00000008  00000000  00000000  00000614  2**2
                  ALLOC
 38 .debug_frame  00000190  00000000  00000000  00000614  2**2
                  CONTENTS, RELOC, READONLY, DEBUGGING
 39 .debug_info   00000e78  00000000  00000000  000007a4  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 40 .debug_abbrev 00000404  00000000  00000000  0000161c  2**0
                  CONTENTS, READONLY, DEBUGGING
 41 .debug_loc    000005f1  00000000  00000000  00001a20  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 42 .debug_aranges 00000098  00000000  00000000  00002011  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 43 .debug_ranges 000000a0  00000000  00000000  000020a9  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 44 .debug_line   000005fb  00000000  00000000  00002149  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 45 .debug_str    0000071f  00000000  00000000  00002744  2**0
                  CONTENTS, READONLY, DEBUGGING
 46 .comment      0000003b  00000000  00000000  00002e63  2**0
                  CONTENTS, READONLY
 47 .xtensa.info  00000038  00000000  00000000  00002e9e  2**0
                  CONTENTS, READONLY
 48 .xt.lit       00000068  00000000  00000000  00002ed6  2**0
                  CONTENTS, RELOC, READONLY
 49 .xt.prop      00000408  00000000  00000000  00002f3e  2**0
                  CONTENTS, RELOC, READONLY

portasm.S.obj:     file format elf32-xtensa-le

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .literal      00000074  00000000  00000000  00000034  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  1 .text         000001e0  00000000  00000000  000000a8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  2 .data         00000c0c  00000000  00000000  00000290  2**4
                  CONTENTS, ALLOC, LOAD, DATA
  3 .bss          00000000  00000000  00000000  00000e9c  2**0
                  ALLOC
  4 .xtensa.info  00000038  00000000  00000000  00000e9c  2**0
                  CONTENTS, READONLY
  5 .debug_line   00000432  00000000  00000000  00000ed4  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
  6 .debug_info   00000093  00000000  00000000  00001306  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
  7 .debug_abbrev 00000014  00000000  00000000  00001399  2**0
                  CONTENTS, READONLY, DEBUGGING
  8 .debug_aranges 00000020  00000000  00000000  000013b0  2**3
                  CONTENTS, RELOC, READONLY, DEBUGGING
  9 .xt.lit       00000008  00000000  00000000  000013d0  2**0
                  CONTENTS, RELOC, READONLY
 10 .xt.prop      00000168  00000000  00000000  000013d8  2**0
                  CONTENTS, RELOC, READONLY

queue.c.obj:     file format elf32-xtensa-le

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .literal.prvIsQueueFull 00000008  00000000  00000000  00000034  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  1 .literal.prvCopyDataToQueue 0000000c  00000000  00000000  0000003c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  2 .literal.prvNotifyQueueSetContainer 0000002c  00000000  00000000  00000048  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  3 .literal.prvCopyDataFromQueue 00000004  00000000  00000000  00000074  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  4 .literal.xQueueGenericReset 00000030  00000000  00000000  00000078  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  5 .literal.prvInitialiseNewQueue 00000004  00000000  00000000  000000a8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  6 .literal.xQueueGenericCreate 0000001c  00000000  00000000  000000ac  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  7 .literal.xQueueGetMutexHolder 00000008  00000000  00000000  000000c8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  8 .literal.xQueueCreateCountingSemaphore 00000028  00000000  00000000  000000d0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  9 .literal.xQueueGenericSend 0000007c  00000000  00000000  000000f8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 10 .literal.prvInitialiseMutex 00000008  00000000  00000000  00000174  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 11 .literal.xQueueCreateMutex 00000008  00000000  00000000  0000017c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 12 .literal.xQueueGiveMutexRecursive 0000001c  00000000  00000000  00000184  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 13 .literal.xQueueGenericSendFromISR 0000003c  00000000  00000000  000001a0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 14 .literal.xQueueGiveFromISR 00000030  00000000  00000000  000001dc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 15 .literal.xQueueGenericReceive 00000078  00000000  00000000  0000020c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 16 .literal.xQueueTakeMutexRecursive 0000001c  00000000  00000000  00000284  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 17 .literal.xQueueReceiveFromISR 00000030  00000000  00000000  000002a0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 18 .literal.xQueuePeekFromISR 00000034  00000000  00000000  000002d0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 19 .literal.uxQueueMessagesWaiting 0000001c  00000000  00000000  00000304  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 20 .literal.uxQueueSpacesAvailable 0000001c  00000000  00000000  00000320  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 21 .literal.uxQueueMessagesWaitingFromISR 0000001c  00000000  00000000  0000033c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 22 .literal.vQueueDelete 00000018  00000000  00000000  00000358  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 23 .literal.xQueueIsQueueEmptyFromISR 0000001c  00000000  00000000  00000370  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 24 .literal.xQueueIsQueueFullFromISR 0000001c  00000000  00000000  0000038c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 25 .literal.vQueueWaitForMessageRestricted 0000000c  00000000  00000000  000003a8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 26 .literal.xQueueCreateSet 00000004  00000000  00000000  000003b4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 27 .literal.xQueueSelectFromSet 00000004  00000000  00000000  000003b8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 28 .literal.xQueueSelectFromSetFromISR 00000004  00000000  00000000  000003bc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 29 .text         00000000  00000000  00000000  000003c0  2**0
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
 30 .data         00000000  00000000  00000000  000003c0  2**0
                  CONTENTS, ALLOC, LOAD, DATA
 31 .bss          00000000  00000000  00000000  000003c0  2**0
                  ALLOC
 32 .text.prvIsQueueEmpty 00000012  00000000  00000000  000003c0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 33 .text.prvIsQueueFull 0000002a  00000000  00000000  000003d4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 34 .text.prvCopyDataToQueue 0000009e  00000000  00000000  00000400  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 35 .rodata.str1.4 00000050  00000000  00000000  000004a0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 36 .text.prvNotifyQueueSetContainer 00000076  00000000  00000000  000004f0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 37 .text.prvCopyDataFromQueue 00000024  00000000  00000000  00000568  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 38 .text.xQueueGenericReset 00000096  00000000  00000000  0000058c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 39 .text.prvInitialiseNewQueue 00000023  00000000  00000000  00000624  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 40 .text.xQueueGenericCreate 0000004d  00000000  00000000  00000648  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 41 .text.xQueueGetMutexHolder 00000023  00000000  00000000  00000698  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 42 .text.xQueueCreateCountingSemaphore 0000006a  00000000  00000000  000006bc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 43 .text.xQueueGenericSend 0000018c  00000000  00000000  00000728  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 44 .text.prvInitialiseMutex 00000026  00000000  00000000  000008b4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 45 .text.xQueueCreateMutex 0000001a  00000000  00000000  000008dc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 46 .text.xQueueGiveMutexRecursive 0000004c  00000000  00000000  000008f8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 47 .text.xQueueGenericSendFromISR 000000e6  00000000  00000000  00000944  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 48 .text.xQueueGiveFromISR 000000c2  00000000  00000000  00000a2c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 49 .text.xQueueGenericReceive 00000178  00000000  00000000  00000af0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 50 .text.xQueueTakeMutexRecursive 00000051  00000000  00000000  00000c68  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 51 .text.xQueueReceiveFromISR 000000a6  00000000  00000000  00000cbc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 52 .text.xQueuePeekFromISR 00000098  00000000  00000000  00000d64  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 53 .text.uxQueueMessagesWaiting 00000038  00000000  00000000  00000dfc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 54 .text.uxQueueSpacesAvailable 0000003e  00000000  00000000  00000e34  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 55 .text.uxQueueMessagesWaitingFromISR 00000038  00000000  00000000  00000e74  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 56 .text.vQueueDelete 00000028  00000000  00000000  00000eac  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 57 .text.xQueueIsQueueEmptyFromISR 00000042  00000000  00000000  00000ed4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 58 .text.xQueueIsQueueFullFromISR 00000044  00000000  00000000  00000f18  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 59 .text.vQueueWaitForMessageRestricted 0000002a  00000000  00000000  00000f5c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 60 .text.xQueueCreateSet 00000014  00000000  00000000  00000f88  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 61 .text.xQueueAddToSet 0000001e  00000000  00000000  00000f9c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 62 .text.xQueueRemoveFromSet 00000020  00000000  00000000  00000fbc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 63 .text.xQueueSelectFromSet 00000018  00000000  00000000  00000fdc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 64 .text.xQueueSelectFromSetFromISR 00000015  00000000  00000000  00000ff4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 65 .rodata.__FUNCTION__$5459 00000019  00000000  00000000  0000100c  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 66 .rodata.__FUNCTION__$5449 0000001a  00000000  00000000  00001028  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 67 .rodata.__FUNCTION__$5429 0000000d  00000000  00000000  00001044  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 68 .rodata.__FUNCTION__$5424 0000001e  00000000  00000000  00001054  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 69 .rodata.__FUNCTION__$5418 00000017  00000000  00000000  00001074  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 70 .rodata.__FUNCTION__$5412 00000017  00000000  00000000  0000108c  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 71 .rodata.__FUNCTION__$5406 00000012  00000000  00000000  000010a4  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 72 .rodata.__FUNCTION__$5397 00000015  00000000  00000000  000010b8  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 73 .rodata.__FUNCTION__$5387 00000015  00000000  00000000  000010d0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 74 .rodata.__FUNCTION__$5376 00000012  00000000  00000000  000010e8  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 75 .rodata.__FUNCTION__$5368 00000019  00000000  00000000  000010fc  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 76 .rodata.__FUNCTION__$5495 0000001b  00000000  00000000  00001118  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 77 .rodata.__FUNCTION__$5357 00000012  00000000  00000000  00001134  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 78 .rodata.__FUNCTION__$5346 0000001e  00000000  00000000  00001148  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 79 .rodata.__FUNCTION__$5340 00000019  00000000  00000000  00001168  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 80 .rodata.__FUNCTION__$5333 00000019  00000000  00000000  00001184  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 81 .rodata.__FUNCTION__$5306 00000014  00000000  00000000  000011a0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 82 .rodata.__FUNCTION__$5297 00000013  00000000  00000000  000011b4  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 83 .debug_frame  00000310  00000000  00000000  000011c8  2**2
                  CONTENTS, RELOC, READONLY, DEBUGGING
 84 .debug_info   0000226e  00000000  00000000  000014d8  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 85 .debug_abbrev 00000247  00000000  00000000  00003746  2**0
                  CONTENTS, READONLY, DEBUGGING
 86 .debug_loc    000010f0  00000000  00000000  0000398d  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 87 .debug_aranges 00000118  00000000  00000000  00004a7d  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 88 .debug_ranges 00000108  00000000  00000000  00004b95  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 89 .debug_line   00000e69  00000000  00000000  00004c9d  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 90 .debug_str    000009f1  00000000  00000000  00005b06  2**0
                  CONTENTS, READONLY, DEBUGGING
 91 .comment      0000003b  00000000  00000000  000064f7  2**0
                  CONTENTS, READONLY
 92 .xtensa.info  00000038  00000000  00000000  00006532  2**0
                  CONTENTS, READONLY
 93 .xt.lit       000000e8  00000000  00000000  0000656a  2**0
                  CONTENTS, RELOC, READONLY
 94 .xt.prop      00000e10  00000000  00000000  00006652  2**0
                  CONTENTS, RELOC, READONLY

ringbuf.c.obj:     file format elf32-xtensa-le

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .literal.returnItemToRingbufBytebuf 0000001c  00000000  00000000  00000034  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  1 .literal.returnItemToRingbufDefault 00000044  00000000  00000000  00000050  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  2 .literal.getItemFromRingbufDefault 00000024  00000000  00000000  00000094  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  3 .literal.copyItemToRingbufNoSplit 00000024  00000000  00000000  000000b8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  4 .literal.copyItemToRingbufByteBuf 00000008  00000000  00000000  000000dc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  5 .literal.copyItemToRingbufAllowSplit 00000034  00000000  00000000  000000e4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  6 .literal.xRingbufferReceiveGeneric 00000020  00000000  00000000  00000118  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  7 .literal.xRingbufferPrintInfo 00000020  00000000  00000000  00000138  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  8 .literal.xRingbufferGetCurFreeSize 0000001c  00000000  00000000  00000158  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  9 .literal.xRingbufferCreate 00000064  00000000  00000000  00000174  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 10 .literal.xRingbufferCreateNoSplit 00000004  00000000  00000000  000001d8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 11 .literal.vRingbufferDelete 00000010  00000000  00000000  000001dc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 12 .literal.xRingbufferGetMaxItemSize 00000014  00000000  00000000  000001ec  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 13 .literal.xRingbufferIsNextItemWrapped 00000014  00000000  00000000  00000200  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 14 .literal.xRingbufferSend 0000003c  00000000  00000000  00000214  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 15 .literal.xRingbufferSendFromISR 00000024  00000000  00000000  00000250  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 16 .literal.xRingbufferReceive 00000004  00000000  00000000  00000274  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 17 .literal.xRingbufferReceiveFromISR 0000001c  00000000  00000000  00000278  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 18 .literal.xRingbufferReceiveUpTo 00000020  00000000  00000000  00000294  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 19 .literal.xRingbufferReceiveUpToFromISR 00000024  00000000  00000000  000002b4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 20 .literal.vRingbufferReturnItem 0000000c  00000000  00000000  000002d8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 21 .literal.vRingbufferReturnItemFromISR 0000000c  00000000  00000000  000002e4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 22 .literal.xRingbufferAddToQueueSetRead 00000018  00000000  00000000  000002f0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 23 .literal.xRingbufferAddToQueueSetWrite 00000018  00000000  00000000  00000308  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 24 .literal.xRingbufferRemoveFromQueueSetRead 00000018  00000000  00000000  00000320  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 25 .literal.xRingbufferRemoveFromQueueSetWrite 00000018  00000000  00000000  00000338  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 26 .text         00000000  00000000  00000000  00000350  2**0
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
 27 .data         00000000  00000000  00000000  00000350  2**0
                  CONTENTS, ALLOC, LOAD, DATA
 28 .bss          00000000  00000000  00000000  00000350  2**0
                  ALLOC
 29 .text.ringbufferFreeMem 00000015  00000000  00000000  00000350  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 30 .text.getItemFromRingbufByteBuf 00000056  00000000  00000000  00000368  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 31 .text.getCurFreeSizeByteBuf 00000015  00000000  00000000  000003c0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 32 .text.getCurFreeSizeAllowSplit 00000030  00000000  00000000  000003d8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 33 .text.getCurFreeSizeNoSplit 00000024  00000000  00000000  00000408  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 34 .rodata.str1.4 00000083  00000000  00000000  0000042c  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 35 .text.returnItemToRingbufBytebuf 00000045  00000000  00000000  000004b0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 36 .text.returnItemToRingbufDefault 00000136  00000000  00000000  000004f8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 37 .text.getItemFromRingbufDefault 000000a5  00000000  00000000  00000630  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 38 .text.copyItemToRingbufNoSplit 000000b0  00000000  00000000  000006d8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 39 .text.copyItemToRingbufByteBuf 00000046  00000000  00000000  00000788  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 40 .text.copyItemToRingbufAllowSplit 00000124  00000000  00000000  000007d0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 41 .text.xRingbufferReceiveGeneric 00000070  00000000  00000000  000008f4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 42 .text.xRingbufferPrintInfo 00000046  00000000  00000000  00000964  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 43 .text.xRingbufferGetCurFreeSize 00000043  00000000  00000000  000009ac  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 44 .text.xRingbufferCreate 00000118  00000000  00000000  000009f0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 45 .text.xRingbufferCreateNoSplit 0000001c  00000000  00000000  00000b08  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 46 .text.vRingbufferDelete 0000002c  00000000  00000000  00000b24  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 47 .text.xRingbufferGetMaxItemSize 00000022  00000000  00000000  00000b50  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 48 .text.xRingbufferIsNextItemWrapped 00000030  00000000  00000000  00000b74  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 49 .text.xRingbufferSend 000000d4  00000000  00000000  00000ba4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 50 .text.xRingbufferSendFromISR 00000062  00000000  00000000  00000c78  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 51 .text.xRingbufferReceive 00000015  00000000  00000000  00000cdc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 52 .text.xRingbufferReceiveFromISR 00000040  00000000  00000000  00000cf4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 53 .text.xRingbufferReceiveUpTo 00000054  00000000  00000000  00000d34  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 54 .text.xRingbufferReceiveUpToFromISR 00000064  00000000  00000000  00000d88  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 55 .text.vRingbufferReturnItem 00000030  00000000  00000000  00000dec  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 56 .text.vRingbufferReturnItemFromISR 0000002c  00000000  00000000  00000e1c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 57 .text.xRingbufferAddToQueueSetRead 0000002c  00000000  00000000  00000e48  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 58 .text.xRingbufferAddToQueueSetWrite 0000002c  00000000  00000000  00000e74  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 59 .text.xRingbufferRemoveFromQueueSetRead 0000002c  00000000  00000000  00000ea0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 60 .text.xRingbufferRemoveFromQueueSetWrite 0000002c  00000000  00000000  00000ecc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 61 .rodata.__FUNCTION__$5577 00000023  00000000  00000000  00000ef8  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 62 .rodata.__FUNCTION__$5571 00000022  00000000  00000000  00000f1c  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 63 .rodata.__FUNCTION__$5565 0000001e  00000000  00000000  00000f40  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 64 .rodata.__FUNCTION__$5559 0000001d  00000000  00000000  00000f60  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 65 .rodata.__FUNCTION__$5542 0000001e  00000000  00000000  00000f80  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 66 .rodata.__FUNCTION__$5534 00000017  00000000  00000000  00000fa0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 67 .rodata.__FUNCTION__$5527 0000001a  00000000  00000000  00000fb8  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 68 .rodata.__FUNCTION__$5508 0000001a  00000000  00000000  00000fd4  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 69 .rodata.__FUNCTION__$5497 00000017  00000000  00000000  00000ff0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 70 .rodata.__FUNCTION__$5482 00000010  00000000  00000000  00001008  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 71 .rodata.__FUNCTION__$5469 0000001d  00000000  00000000  00001018  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 72 .rodata.__FUNCTION__$5464 0000001a  00000000  00000000  00001038  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 73 .rodata.__FUNCTION__$5379 0000001c  00000000  00000000  00001054  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 74 .rodata.__FUNCTION__$5395 0000001a  00000000  00000000  00001070  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 75 .rodata.__FUNCTION__$5408 0000001b  00000000  00000000  0000108c  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 76 .rodata.__FUNCTION__$5418 0000001b  00000000  00000000  000010a8  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 77 .rodata.__FUNCTION__$5369 00000019  00000000  00000000  000010c4  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 78 .rodata.__FUNCTION__$5450 00000012  00000000  00000000  000010e0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 79 .rodata.__FUNCTION__$5428 0000001a  00000000  00000000  000010f4  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 80 .rodata.__FUNCTION__$5423 00000015  00000000  00000000  00001110  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 81 .debug_frame  000002f8  00000000  00000000  00001128  2**2
                  CONTENTS, RELOC, READONLY, DEBUGGING
 82 .debug_info   00001dc4  00000000  00000000  00001420  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 83 .debug_abbrev 000002c9  00000000  00000000  000031e4  2**0
                  CONTENTS, READONLY, DEBUGGING
 84 .debug_loc    00000df4  00000000  00000000  000034ad  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 85 .debug_aranges 00000110  00000000  00000000  000042a1  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 86 .debug_ranges 00000130  00000000  00000000  000043b1  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 87 .debug_line   00000ce4  00000000  00000000  000044e1  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 88 .debug_str    000008b1  00000000  00000000  000051c5  2**0
                  CONTENTS, READONLY, DEBUGGING
 89 .comment      0000003b  00000000  00000000  00005a76  2**0
                  CONTENTS, READONLY
 90 .xtensa.info  00000038  00000000  00000000  00005ab1  2**0
                  CONTENTS, READONLY
 91 .xt.lit       000000d0  00000000  00000000  00005ae9  2**0
                  CONTENTS, RELOC, READONLY
 92 .xt.prop      00000bb8  00000000  00000000  00005bb9  2**0
                  CONTENTS, RELOC, READONLY

tasks.c.obj:     file format elf32-xtensa-le

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .literal.prvResetNextTaskUnblockTime 00000008  00000000  00000000  00000034  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  1 .literal.prvTaskGetSnapshotsFromList 00000004  00000000  00000000  0000003c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  2 .literal.prvDeleteTLS 00000018  00000000  00000000  00000040  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  3 .literal.prvTaskIsTaskSuspended 0000001c  00000000  00000000  00000058  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  4 .literal.prvInitialiseNewTask 00000018  00000000  00000000  00000074  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  5 .literal.prvInitialiseTaskLists 00000040  00000000  00000000  0000008c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  6 .literal.prvDeleteTCB 0000002c  00000000  00000000  000000cc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  7 .literal.prvAddCurrentTaskToDelayedList 0000001c  00000000  00000000  000000f8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  8 .literal.taskYIELD_OTHER_CORE 00000010  00000000  00000000  00000114  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  9 .literal.vTaskEndScheduler 00000008  00000000  00000000  00000124  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 10 .literal.vTaskSuspendAll 00000008  00000000  00000000  0000012c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 11 .literal.uxTaskGetNumberOfTasks 00000004  00000000  00000000  00000134  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 12 .literal.xTaskGetIdleTaskHandle 0000001c  00000000  00000000  00000138  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 13 .literal.xTaskGetIdleTaskHandleForCPU 0000001c  00000000  00000000  00000154  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 14 .literal.vTaskSwitchContext 0000003c  00000000  00000000  00000170  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 15 .literal.vTaskSetTimeOutState 00000020  00000000  00000000  000001ac  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 16 .literal.vTaskMissedYield 00000004  00000000  00000000  000001cc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 17 .literal.vTaskAllocateMPURegions 00000020  00000000  00000000  000001d0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 18 .literal.xTaskGetCurrentTaskHandle 00000008  00000000  00000000  000001f0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 19 .literal.__getreent 00000008  00000000  00000000  000001f8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 20 .literal.pcTaskGetName 0000001c  00000000  00000000  00000200  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 21 .literal.pvTaskGetThreadLocalStoragePointer 00000004  00000000  00000000  0000021c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 22 .literal.xTaskGetAffinity 00000004  00000000  00000000  00000220  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 23 .literal.uxTaskGetStackHighWaterMark 00000008  00000000  00000000  00000224  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 24 .literal.pxTaskGetStackStart 00000004  00000000  00000000  0000022c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 25 .literal.xTaskGetCurrentTaskHandleForCPU 00000004  00000000  00000000  00000230  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 26 .literal.xTaskGetSchedulerState 0000000c  00000000  00000000  00000234  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 27 .literal.vTaskEnterCritical 00000034  00000000  00000000  00000240  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 28 .literal.vTaskExitCritical 00000030  00000000  00000000  00000274  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 29 .literal.prvAddNewTaskToReadyList 00000058  00000000  00000000  000002a4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 30 .literal.xTaskCreateRestricted 00000028  00000000  00000000  000002fc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 31 .literal.xTaskCreatePinnedToCore 00000018  00000000  00000000  00000324  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 32 .literal.vTaskStartScheduler 00000038  00000000  00000000  0000033c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 33 .literal.vTaskDelete 00000060  00000000  00000000  00000374  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 34 .literal.vTaskDelayUntil 00000048  00000000  00000000  000003d4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 35 .literal.vTaskDelay 00000038  00000000  00000000  0000041c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 36 .literal.eTaskGetState 00000038  00000000  00000000  00000454  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 37 .literal.uxTaskPriorityGet 00000010  00000000  00000000  0000048c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 38 .literal.uxTaskPriorityGetFromISR 00000010  00000000  00000000  0000049c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 39 .literal.vTaskPrioritySet 00000044  00000000  00000000  000004ac  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 40 .literal.vTaskSuspend 00000060  00000000  00000000  000004f0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 41 .literal.vTaskResume 00000044  00000000  00000000  00000550  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 42 .literal.xTaskResumeFromISR 0000004c  00000000  00000000  00000594  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 43 .literal.prvCheckTasksWaitingTermination 00000030  00000000  00000000  000005e0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 44 .literal.prvIdleTask 00000008  00000000  00000000  00000610  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 45 .literal.xTaskGetTickCount 00000010  00000000  00000000  00000618  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 46 .literal.xTaskGetTickCountFromISR 00000010  00000000  00000000  00000628  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 47 .literal.xTaskIncrementTick 00000078  00000000  00000000  00000638  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 48 .literal.xTaskResumeAll 0000005c  00000000  00000000  000006b0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 49 .literal.vTaskPlaceOnEventList 00000040  00000000  00000000  0000070c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 50 .literal.vTaskPlaceOnUnorderedEventList 00000054  00000000  00000000  0000074c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 51 .literal.vTaskPlaceOnEventListRestricted 00000038  00000000  00000000  000007a0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 52 .literal.xTaskRemoveFromEventList 00000058  00000000  00000000  000007d8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 53 .literal.xTaskRemoveFromUnorderedEventList 0000005c  00000000  00000000  00000830  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 54 .literal.xTaskCheckForTimeOut 0000003c  00000000  00000000  0000088c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 55 .literal.vTaskSetThreadLocalStoragePointerAndDelCallback 00000010  00000000  00000000  000008c8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 56 .literal.vTaskSetThreadLocalStoragePointer 00000004  00000000  00000000  000008d8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 57 .literal.vTaskPriorityInherit 0000002c  00000000  00000000  000008dc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 58 .literal.xTaskPriorityDisinherit 00000040  00000000  00000000  00000908  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 59 .literal.uxTaskResetEventItemValue 00000010  00000000  00000000  00000948  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 60 .literal.pvTaskIncrementMutexHeldCount 00000010  00000000  00000000  00000958  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 61 .literal.ulTaskNotifyTake 00000030  00000000  00000000  00000968  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 62 .literal.xTaskNotifyWait 00000030  00000000  00000000  00000998  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 63 .literal.xTaskNotify 00000050  00000000  00000000  000009c8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 64 .literal.xTaskNotifyFromISR 00000058  00000000  00000000  00000a18  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 65 .literal.vTaskNotifyGiveFromISR 00000058  00000000  00000000  00000a70  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 66 .literal.uxTaskGetSnapshotAll 00000030  00000000  00000000  00000ac8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 67 .text         00000000  00000000  00000000  00000af8  2**0
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
 68 .data         00000000  00000000  00000000  00000af8  2**0
                  CONTENTS, ALLOC, LOAD, DATA
 69 .bss          00000000  00000000  00000000  00000af8  2**0
                  ALLOC
 70 .text.prvTaskCheckFreeStackSpace 00000019  00000000  00000000  00000af8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 71 .text.prvResetNextTaskUnblockTime 00000034  00000000  00000000  00000b14  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 72 .text.prvTaskGetSnapshot 00000036  00000000  00000000  00000b48  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 73 .text.prvTaskGetSnapshotsFromList 00000047  00000000  00000000  00000b80  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 74 .rodata.str1.4 00000161  00000000  00000000  00000bc8  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 75 .text.prvDeleteTLS 00000040  00000000  00000000  00000d2c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 76 .text.prvTaskIsTaskSuspended 0000004e  00000000  00000000  00000d6c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 77 .text.prvInitialiseNewTask 000000d0  00000000  00000000  00000dbc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 78 .text.prvInitialiseTaskLists 0000006e  00000000  00000000  00000e8c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 79 .text.prvDeleteTCB 0000005d  00000000  00000000  00000efc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 80 .text.prvAddCurrentTaskToDelayedList 0000006a  00000000  00000000  00000f5c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 81 .text.taskYIELD_OTHER_CORE 00000053  00000000  00000000  00000fc8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 82 .text.vTaskEndScheduler 00000018  00000000  00000000  0000101c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 83 .text.vTaskSuspendAll 00000026  00000000  00000000  00001034  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 84 .text.uxTaskGetNumberOfTasks 0000000d  00000000  00000000  0000105c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 85 .text.xTaskGetIdleTaskHandle 0000003b  00000000  00000000  0000106c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 86 .text.xTaskGetIdleTaskHandleForCPU 0000002e  00000000  00000000  000010a8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 87 .text.vTaskSwitchContext 0000028a  00000000  00000000  000010d8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 88 .text.vTaskSetTimeOutState 00000034  00000000  00000000  00001364  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 89 .text.vTaskMissedYield 00000018  00000000  00000000  00001398  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 90 .text.vTaskAllocateMPURegions 00000028  00000000  00000000  000013b0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 91 .text.xTaskGetCurrentTaskHandle 0000001f  00000000  00000000  000013d8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 92 .text.__getreent 00000019  00000000  00000000  000013f8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 93 .text.pcTaskGetName 0000002d  00000000  00000000  00001414  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 94 .text.pvTaskGetThreadLocalStoragePointer 0000001e  00000000  00000000  00001444  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 95 .text.xTaskGetAffinity 00000013  00000000  00000000  00001464  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 96 .text.uxTaskGetStackHighWaterMark 0000001a  00000000  00000000  00001478  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 97 .text.pxTaskGetStackStart 00000012  00000000  00000000  00001494  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 98 .text.xTaskGetCurrentTaskHandleForCPU 00000018  00000000  00000000  000014a8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 99 .text.xTaskGetSchedulerState 00000037  00000000  00000000  000014c0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
100 .text.vTaskEnterCritical 000000b3  00000000  00000000  000014f8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
101 .text.vTaskExitCritical 0000008a  00000000  00000000  000015ac  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
102 .text.prvAddNewTaskToReadyList 00000173  00000000  00000000  00001638  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
103 .text.xTaskCreateRestricted 00000074  00000000  00000000  000017ac  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
104 .text.xTaskCreatePinnedToCore 00000076  00000000  00000000  00001820  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
105 .text.vTaskStartScheduler 00000072  00000000  00000000  00001898  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
106 .text.vTaskDelete 00000122  00000000  00000000  0000190c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
107 .text.vTaskDelayUntil 000000e7  00000000  00000000  00001a30  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
108 .text.vTaskDelay 00000080  00000000  00000000  00001b18  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
109 .text.eTaskGetState 000000aa  00000000  00000000  00001b98  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
110 .text.uxTaskPriorityGet 00000023  00000000  00000000  00001c44  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
111 .text.uxTaskPriorityGetFromISR 00000023  00000000  00000000  00001c68  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
112 .text.vTaskPrioritySet 00000143  00000000  00000000  00001c8c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
113 .text.vTaskSuspend 00000106  00000000  00000000  00001dd0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
114 .text.vTaskResume 000000d8  00000000  00000000  00001ed8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
115 .text.xTaskResumeFromISR 000000f6  00000000  00000000  00001fb0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
116 .text.prvCheckTasksWaitingTermination 000000c6  00000000  00000000  000020a8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
117 .text.prvIdleTask 00000012  00000000  00000000  00002170  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
118 .text.xTaskGetTickCount 00000020  00000000  00000000  00002184  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
119 .text.xTaskGetTickCountFromISR 00000020  00000000  00000000  000021a4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
120 .text.xTaskIncrementTick 000001ce  00000000  00000000  000021c4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
121 .text.xTaskResumeAll 00000186  00000000  00000000  00002394  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
122 .text.vTaskPlaceOnEventList 00000097  00000000  00000000  0000251c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
123 .text.vTaskPlaceOnUnorderedEventList 000000cf  00000000  00000000  000025b4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
124 .text.vTaskPlaceOnEventListRestricted 0000006f  00000000  00000000  00002684  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
125 .text.xTaskRemoveFromEventList 00000143  00000000  00000000  000026f4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
126 .text.xTaskRemoveFromUnorderedEventList 000000ff  00000000  00000000  00002838  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
127 .text.xTaskCheckForTimeOut 00000096  00000000  00000000  00002938  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
128 .text.vTaskSetThreadLocalStoragePointerAndDelCallback 0000002f  00000000  00000000  000029d0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
129 .text.vTaskSetThreadLocalStoragePointer 00000013  00000000  00000000  00002a00  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
130 .text.vTaskPriorityInherit 000000db  00000000  00000000  00002a14  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
131 .text.xTaskPriorityDisinherit 000000aa  00000000  00000000  00002af0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
132 .text.uxTaskResetEventItemValue 0000003e  00000000  00000000  00002b9c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
133 .text.pvTaskIncrementMutexHeldCount 00000054  00000000  00000000  00002bdc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
134 .text.ulTaskNotifyTake 0000012e  00000000  00000000  00002c30  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
135 .text.xTaskNotifyWait 00000156  00000000  00000000  00002d60  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
136 .text.xTaskNotify 0000014a  00000000  00000000  00002eb8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
137 .text.xTaskNotifyFromISR 00000172  00000000  00000000  00003004  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
138 .text.vTaskNotifyGiveFromISR 0000011c  00000000  00000000  00003178  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
139 .text.uxTaskGetSnapshotAll 0000009c  00000000  00000000  00003294  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
140 .rodata.__FUNCTION__$5930 00000017  00000000  00000000  00003330  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
141 .rodata.__FUNCTION__$5917 00000013  00000000  00000000  00003348  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
142 .rodata.__FUNCTION__$5901 0000000c  00000000  00000000  0000335c  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
143 .rodata.__func__$5851 00000029  00000000  00000000  00003368  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
144 .rodata.__func__$5846 00000029  00000000  00000000  00003394  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
145 .rodata.__FUNCTION__$5808 00000018  00000000  00000000  000033c0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
146 .rodata.__FUNCTION__$5718 00000018  00000000  00000000  000033d8  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
147 .rodata.__FUNCTION__$5683 00000015  00000000  00000000  000033f0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
148 .rodata.__FUNCTION__$5677 00000015  00000000  00000000  00003408  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
149 .rodata.__FUNCTION__$5673 00000022  00000000  00000000  00003420  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
150 .rodata.__FUNCTION__$5663 00000019  00000000  00000000  00003444  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
151 .rodata.__FUNCTION__$5654 00000020  00000000  00000000  00003460  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
152 .rodata.__FUNCTION__$5648 0000001f  00000000  00000000  00003480  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
153 .rodata.__FUNCTION__$5641 00000016  00000000  00000000  000034a0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
154 .rodata.ucExpectedStackBytes$5613 00000014  00000000  00000000  000034b8  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
155 .rodata.__FUNCTION__$5605 00000013  00000000  00000000  000034cc  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
156 .rodata.__FUNCTION__$5596 0000001d  00000000  00000000  000034e0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
157 .rodata.__FUNCTION__$5591 00000017  00000000  00000000  00003500  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
158 .rodata.__FUNCTION__$5587 00000012  00000000  00000000  00003518  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
159 .rodata.__FUNCTION__$5565 0000000f  00000000  00000000  0000352c  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
160 .rodata.__FUNCTION__$5547 00000014  00000000  00000000  0000353c  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
161 .rodata.__FUNCTION__$5536 00000013  00000000  00000000  00003550  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
162 .rodata.__FUNCTION__$5525 00000017  00000000  00000000  00003564  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
163 .rodata.__FUNCTION__$5530 0000000c  00000000  00000000  0000357c  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
164 .rodata.__FUNCTION__$5519 0000000d  00000000  00000000  00003588  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
165 .rodata.__FUNCTION__$5513 00000011  00000000  00000000  00003598  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
166 .rodata.__FUNCTION__$5494 0000000e  00000000  00000000  000035ac  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
167 .rodata.__FUNCTION__$5485 0000000b  00000000  00000000  000035bc  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
168 .rodata.__FUNCTION__$5478 00000010  00000000  00000000  000035c8  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
169 .rodata.__FUNCTION__$5772 0000000d  00000000  00000000  000035d8  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
170 .rodata.__FUNCTION__$5776 0000000d  00000000  00000000  000035e8  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
171 .rodata.__FUNCTION__$5470 0000000c  00000000  00000000  000035f8  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
172 .rodata.__FUNCTION__$5463 00000019  00000000  00000000  00003604  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
173 .rodata.__FUNCTION__$5421 00000016  00000000  00000000  00003620  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
174 .bss.xSwitchingContext 00000008  00000000  00000000  00003638  2**2
                  ALLOC
175 .data.xTickCountMutex 00000008  00000000  00000000  00003638  2**2
                  CONTENTS, ALLOC, LOAD, DATA
176 .data.xTaskQueueMutex 00000008  00000000  00000000  00003640  2**2
                  CONTENTS, ALLOC, LOAD, DATA
177 .bss.uxSchedulerSuspended 00000008  00000000  00000000  00003648  2**2
                  ALLOC
178 .data.xNextTaskUnblockTime 00000004  00000000  00000000  00003648  2**2
                  CONTENTS, ALLOC, LOAD, DATA
179 .bss.uxTaskNumber 00000004  00000000  00000000  0000364c  2**2
                  ALLOC
180 .bss.xNumOfOverflows 00000004  00000000  00000000  0000364c  2**2
                  ALLOC
181 .bss.xYieldPending 00000008  00000000  00000000  0000364c  2**2
                  ALLOC
182 .bss.uxPendedTicks 00000004  00000000  00000000  0000364c  2**2
                  ALLOC
183 .bss.xSchedulerRunning 00000004  00000000  00000000  0000364c  2**2
                  ALLOC
184 .bss.uxTopReadyPriority 00000004  00000000  00000000  0000364c  2**2
                  ALLOC
185 .bss.xTickCount 00000004  00000000  00000000  0000364c  2**2
                  ALLOC
186 .bss.uxCurrentNumberOfTasks 00000004  00000000  00000000  0000364c  2**2
                  ALLOC
187 .bss.xIdleTaskHandle 00000008  00000000  00000000  0000364c  2**2
                  ALLOC
188 .bss.xSuspendedTaskList 00000014  00000000  00000000  0000364c  2**2
                  ALLOC
189 .bss.uxTasksDeleted 00000004  00000000  00000000  0000364c  2**2
                  ALLOC
190 .bss.xTasksWaitingTermination 00000014  00000000  00000000  0000364c  2**2
                  ALLOC
191 .bss.xPendingReadyList 00000028  00000000  00000000  0000364c  2**2
                  ALLOC
192 .bss.pxOverflowDelayedTaskList 00000004  00000000  00000000  0000364c  2**2
                  ALLOC
193 .bss.pxDelayedTaskList 00000004  00000000  00000000  0000364c  2**2
                  ALLOC
194 .bss.xDelayedTaskList2 00000014  00000000  00000000  0000364c  2**2
                  ALLOC
195 .bss.xDelayedTaskList1 00000014  00000000  00000000  0000364c  2**2
                  ALLOC
196 .bss.pxReadyTasksLists 000001f4  00000000  00000000  0000364c  2**2
                  ALLOC
197 .bss.pxCurrentTCB 00000008  00000000  00000000  0000364c  2**2
                  ALLOC
198 .debug_frame  00000688  00000000  00000000  0000364c  2**2
                  CONTENTS, RELOC, READONLY, DEBUGGING
199 .debug_info   00005e20  00000000  00000000  00003cd4  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
200 .debug_abbrev 0000046f  00000000  00000000  00009af4  2**0
                  CONTENTS, READONLY, DEBUGGING
201 .debug_loc    000022ef  00000000  00000000  00009f63  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
202 .debug_aranges 00000240  00000000  00000000  0000c252  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
203 .debug_ranges 000002c8  00000000  00000000  0000c492  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
204 .debug_line   0000291c  00000000  00000000  0000c75a  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
205 .debug_str    0000190d  00000000  00000000  0000f076  2**0
                  CONTENTS, READONLY, DEBUGGING
206 .comment      0000003b  00000000  00000000  00010983  2**0
                  CONTENTS, READONLY
207 .xtensa.info  00000038  00000000  00000000  000109be  2**0
                  CONTENTS, READONLY
208 .xt.lit       00000218  00000000  00000000  000109f6  2**0
                  CONTENTS, RELOC, READONLY
209 .xt.prop      00002154  00000000  00000000  00010c0e  2**0
                  CONTENTS, RELOC, READONLY

timers.c.obj:     file format elf32-xtensa-le

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .literal.prvGetNextExpireTime 00000004  00000000  00000000  00000034  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  1 .literal.prvInsertTimerInActiveList 00000010  00000000  00000000  00000038  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  2 .literal.prvCheckForValidListAndQueue 00000044  00000000  00000000  00000048  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  3 .literal.prvInitialiseNewTimer 0000001c  00000000  00000000  0000008c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  4 .literal.xTimerCreateTimerTask 0000002c  00000000  00000000  000000a8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  5 .literal.xTimerCreate 00000008  00000000  00000000  000000d4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  6 .literal.xTimerGenericCommand 00000014  00000000  00000000  000000dc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  7 .literal.prvSwitchTimerLists 00000028  00000000  00000000  000000f0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  8 .literal.prvSampleTimeNow 0000000c  00000000  00000000  00000118  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  9 .literal.prvProcessExpiredTimer 00000024  00000000  00000000  00000124  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 10 .literal.prvProcessTimerOrBlockTask 00000024  00000000  00000000  00000148  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 11 .literal.prvProcessReceivedCommands 00000040  00000000  00000000  0000016c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 12 .literal.prvTimerTask 0000000c  00000000  00000000  000001ac  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 13 .literal.xTimerGetPeriod 00000014  00000000  00000000  000001b8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 14 .literal.xTimerGetExpiryTime 00000014  00000000  00000000  000001cc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 15 .literal.xTimerIsTimerActive 0000000c  00000000  00000000  000001e0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 16 .literal.vTimerSetTimerID 00000014  00000000  00000000  000001ec  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 17 .literal.xTimerPendFunctionCallFromISR 00000008  00000000  00000000  00000200  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 18 .literal.xTimerPendFunctionCall 0000001c  00000000  00000000  00000208  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 19 .text         00000000  00000000  00000000  00000224  2**0
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
 20 .data         00000000  00000000  00000000  00000224  2**0
                  CONTENTS, ALLOC, LOAD, DATA
 21 .bss          00000000  00000000  00000000  00000224  2**0
                  ALLOC
 22 .text.prvGetNextExpireTime 00000020  00000000  00000000  00000224  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 23 .text.prvInsertTimerInActiveList 00000054  00000000  00000000  00000244  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 24 .rodata.str1.4 00000058  00000000  00000000  00000298  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 25 .text.prvCheckForValidListAndQueue 0000007e  00000000  00000000  000002f0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 26 .text.prvInitialiseNewTimer 0000003a  00000000  00000000  00000370  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 27 .text.xTimerCreateTimerTask 0000004c  00000000  00000000  000003ac  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 28 .text.xTimerCreate 00000026  00000000  00000000  000003f8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 29 .text.xTimerGenericCommand 0000005d  00000000  00000000  00000420  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 30 .text.prvSwitchTimerLists 00000082  00000000  00000000  00000480  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 31 .text.prvSampleTimeNow 0000002f  00000000  00000000  00000504  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 32 .text.prvProcessExpiredTimer 0000005f  00000000  00000000  00000534  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 33 .text.prvProcessTimerOrBlockTask 0000006c  00000000  00000000  00000594  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 34 .text.prvProcessReceivedCommands 000000e6  00000000  00000000  00000600  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 35 .rodata.prvProcessReceivedCommands 00000028  00000000  00000000  000006e8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, DATA
 36 .text.prvTimerTask 0000001d  00000000  00000000  00000710  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 37 .text.xTimerGetPeriod 00000022  00000000  00000000  00000730  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 38 .text.xTimerGetExpiryTime 00000022  00000000  00000000  00000754  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 39 .text.pcTimerGetName 00000007  00000000  00000000  00000778  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
 40 .text.xTimerIsTimerActive 00000024  00000000  00000000  00000780  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 41 .text.pvTimerGetTimerID 00000007  00000000  00000000  000007a4  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
 42 .text.vTimerSetTimerID 00000022  00000000  00000000  000007ac  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 43 .text.xTimerPendFunctionCallFromISR 00000022  00000000  00000000  000007d0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 44 .text.xTimerPendFunctionCall 0000003c  00000000  00000000  000007f4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 45 .rodata.__FUNCTION__$5352 00000017  00000000  00000000  00000830  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 46 .rodata.__FUNCTION__$5335 00000011  00000000  00000000  00000848  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 47 .rodata.__FUNCTION__$5240 00000014  00000000  00000000  0000085c  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 48 .rodata.__FUNCTION__$5234 00000010  00000000  00000000  00000870  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 49 .rodata.__FUNCTION__$5220 00000016  00000000  00000000  00000880  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 50 .rodata.__FUNCTION__$5320 0000001d  00000000  00000000  00000898  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 51 .rodata.__FUNCTION__$5289 0000001b  00000000  00000000  000008b8  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 52 .rodata.__FUNCTION__$5251 00000017  00000000  00000000  000008d4  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 53 .rodata.__FUNCTION__$5313 00000014  00000000  00000000  000008ec  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 54 .bss.xLastTime$5272 00000004  00000000  00000000  00000900  2**2
                  ALLOC
 55 .rodata.__FUNCTION__$5203 00000016  00000000  00000000  00000900  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 56 .data.xTimerMux 00000008  00000000  00000000  00000918  2**2
                  CONTENTS, ALLOC, LOAD, DATA
 57 .bss.xTimerQueue 00000004  00000000  00000000  00000920  2**2
                  ALLOC
 58 .bss.pxOverflowTimerList 00000004  00000000  00000000  00000920  2**2
                  ALLOC
 59 .bss.pxCurrentTimerList 00000004  00000000  00000000  00000920  2**2
                  ALLOC
 60 .bss.xActiveTimerList2 00000014  00000000  00000000  00000920  2**2
                  ALLOC
 61 .bss.xActiveTimerList1 00000014  00000000  00000000  00000920  2**2
                  ALLOC
 62 .debug_frame  00000208  00000000  00000000  00000920  2**2
                  CONTENTS, RELOC, READONLY, DEBUGGING
 63 .debug_info   00001405  00000000  00000000  00000b28  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 64 .debug_abbrev 00000279  00000000  00000000  00001f2d  2**0
                  CONTENTS, READONLY, DEBUGGING
 65 .debug_loc    0000062f  00000000  00000000  000021a6  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 66 .debug_aranges 000000c0  00000000  00000000  000027d5  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 67 .debug_ranges 000000b0  00000000  00000000  00002895  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 68 .debug_line   00000771  00000000  00000000  00002945  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 69 .debug_str    00000934  00000000  00000000  000030b6  2**0
                  CONTENTS, READONLY, DEBUGGING
 70 .comment      0000003b  00000000  00000000  000039ea  2**0
                  CONTENTS, READONLY
 71 .xtensa.info  00000038  00000000  00000000  00003a25  2**0
                  CONTENTS, READONLY
 72 .xt.lit       00000098  00000000  00000000  00003a5d  2**0
                  CONTENTS, RELOC, READONLY
 73 .xt.prop      00000714  00000000  00000000  00003af5  2**0
                  CONTENTS, RELOC, READONLY

xtensa_context.S.obj:     file format elf32-xtensa-le

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .literal      00000024  00000000  00000000  00000034  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  1 .text         0000013a  00000000  00000000  00000058  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  2 .data         00000000  00000000  00000000  00000192  2**0
                  CONTENTS, ALLOC, LOAD, DATA
  3 .bss          00000000  00000000  00000000  00000192  2**0
                  ALLOC
  4 .xtensa.info  00000038  00000000  00000000  00000192  2**0
                  CONTENTS, READONLY
  5 .debug_line   00000313  00000000  00000000  000001ca  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
  6 .debug_info   0000009a  00000000  00000000  000004dd  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
  7 .debug_abbrev 00000014  00000000  00000000  00000577  2**0
                  CONTENTS, READONLY, DEBUGGING
  8 .debug_aranges 00000020  00000000  00000000  00000590  2**3
                  CONTENTS, RELOC, READONLY, DEBUGGING
  9 .xt.lit       00000008  00000000  00000000  000005b0  2**0
                  CONTENTS, RELOC, READONLY
 10 .xt.prop      000000e4  00000000  00000000  000005b8  2**0
                  CONTENTS, RELOC, READONLY

xtensa_init.c.obj:     file format elf32-xtensa-le

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .literal._xt_tick_divisor_init 0000000c  00000000  00000000  00000034  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  1 .literal.xt_clock_freq 00000004  00000000  00000000  00000040  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  2 .text         00000000  00000000  00000000  00000044  2**0
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  3 .data         00000000  00000000  00000000  00000044  2**0
                  CONTENTS, ALLOC, LOAD, DATA
  4 .bss          00000000  00000000  00000000  00000044  2**0
                  ALLOC
  5 .text._xt_tick_divisor_init 0000001f  00000000  00000000  00000044  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  6 .text.xt_clock_freq 0000000d  00000000  00000000  00000064  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  7 .bss._xt_tick_divisor 00000004  00000000  00000000  00000074  2**2
                  ALLOC
  8 .debug_frame  00000040  00000000  00000000  00000074  2**2
                  CONTENTS, RELOC, READONLY, DEBUGGING
  9 .debug_info   000000d4  00000000  00000000  000000b4  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 10 .debug_abbrev 0000008b  00000000  00000000  00000188  2**0
                  CONTENTS, READONLY, DEBUGGING
 11 .debug_aranges 00000028  00000000  00000000  00000213  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 12 .debug_ranges 00000018  00000000  00000000  0000023b  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 13 .debug_line   000000c8  00000000  00000000  00000253  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 14 .debug_str    00000180  00000000  00000000  0000031b  2**0
                  CONTENTS, READONLY, DEBUGGING
 15 .comment      0000003b  00000000  00000000  0000049b  2**0
                  CONTENTS, READONLY
 16 .xtensa.info  00000038  00000000  00000000  000004d6  2**0
                  CONTENTS, READONLY
 17 .xt.lit       00000010  00000000  00000000  0000050e  2**0
                  CONTENTS, RELOC, READONLY
 18 .xt.prop      0000006c  00000000  00000000  0000051e  2**0
                  CONTENTS, RELOC, READONLY

xtensa_intr.c.obj:     file format elf32-xtensa-le

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .literal.xt_unhandled_interrupt 00000008  00000000  00000000  00000034  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  1 .literal.xt_set_exception_handler 00000008  00000000  00000000  0000003c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  2 .literal.xt_set_interrupt_handler 0000000c  00000000  00000000  00000044  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  3 .text         00000000  00000000  00000000  00000050  2**0
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  4 .data         00000000  00000000  00000000  00000050  2**0
                  CONTENTS, ALLOC, LOAD, DATA
  5 .bss          00000000  00000000  00000000  00000050  2**0
                  ALLOC
  6 .rodata.str1.4 00000023  00000000  00000000  00000050  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
  7 .text.xt_unhandled_interrupt 00000016  00000000  00000000  00000074  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  8 .text.xt_set_exception_handler 00000042  00000000  00000000  0000008c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  9 .text.xt_set_interrupt_handler 00000046  00000000  00000000  000000d0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 10 .debug_frame  00000058  00000000  00000000  00000118  2**2
                  CONTENTS, RELOC, READONLY, DEBUGGING
 11 .debug_info   0000042d  00000000  00000000  00000170  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 12 .debug_abbrev 000001b6  00000000  00000000  0000059d  2**0
                  CONTENTS, READONLY, DEBUGGING
 13 .debug_loc    0000015c  00000000  00000000  00000753  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 14 .debug_aranges 00000030  00000000  00000000  000008af  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 15 .debug_ranges 00000020  00000000  00000000  000008df  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 16 .debug_line   000002b8  00000000  00000000  000008ff  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 17 .debug_str    000002a5  00000000  00000000  00000bb7  2**0
                  CONTENTS, READONLY, DEBUGGING
 18 .comment      0000003b  00000000  00000000  00000e5c  2**0
                  CONTENTS, READONLY
 19 .xtensa.info  00000038  00000000  00000000  00000e97  2**0
                  CONTENTS, READONLY
 20 .xt.lit       00000018  00000000  00000000  00000ecf  2**0
                  CONTENTS, RELOC, READONLY
 21 .xt.prop      00000120  00000000  00000000  00000ee7  2**0
                  CONTENTS, RELOC, READONLY

xtensa_intr_asm.S.obj:     file format elf32-xtensa-le

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .text         00000033  00000000  00000000  00000034  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  1 .data         00000400  00000000  00000000  00000068  2**3
                  CONTENTS, ALLOC, LOAD, RELOC, DATA
  2 .bss          00000000  00000000  00000000  00000468  2**0
                  ALLOC
  3 .xtensa.info  00000038  00000000  00000000  00000468  2**0
                  CONTENTS, READONLY
  4 .debug_line   000000da  00000000  00000000  000004a0  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
  5 .debug_info   0000009b  00000000  00000000  0000057a  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
  6 .debug_abbrev 00000014  00000000  00000000  00000615  2**0
                  CONTENTS, READONLY, DEBUGGING
  7 .debug_aranges 00000020  00000000  00000000  00000630  2**3
                  CONTENTS, RELOC, READONLY, DEBUGGING
  8 .xt.prop      00000054  00000000  00000000  00000650  2**0
                  CONTENTS, RELOC, READONLY

xtensa_overlay_os_hook.c.obj:     file format elf32-xtensa-le

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .literal.xt_overlay_init_os 00000008  00000000  00000000  00000034  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  1 .literal.xt_overlay_lock 00000008  00000000  00000000  0000003c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  2 .literal.xt_overlay_unlock 00000008  00000000  00000000  00000044  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  3 .text         00000000  00000000  00000000  0000004c  2**0
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  4 .data         00000000  00000000  00000000  0000004c  2**0
                  CONTENTS, ALLOC, LOAD, DATA
  5 .bss          00000000  00000000  00000000  0000004c  2**0
                  ALLOC
  6 .text.xt_overlay_init_os 00000013  00000000  00000000  0000004c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  7 .text.xt_overlay_lock 00000016  00000000  00000000  00000060  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  8 .text.xt_overlay_unlock 00000016  00000000  00000000  00000078  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  9 .bss.xt_overlay_mutex 00000004  00000000  00000000  00000090  2**2
                  ALLOC
 10 .debug_frame  00000058  00000000  00000000  00000090  2**2
                  CONTENTS, RELOC, READONLY, DEBUGGING
 11 .debug_info   00000193  00000000  00000000  000000e8  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 12 .debug_abbrev 0000008f  00000000  00000000  0000027b  2**0
                  CONTENTS, READONLY, DEBUGGING
 13 .debug_aranges 00000030  00000000  00000000  0000030a  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 14 .debug_ranges 00000020  00000000  00000000  0000033a  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 15 .debug_line   00000192  00000000  00000000  0000035a  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 16 .debug_str    0000025c  00000000  00000000  000004ec  2**0
                  CONTENTS, READONLY, DEBUGGING
 17 .comment      0000003b  00000000  00000000  00000748  2**0
                  CONTENTS, READONLY
 18 .xtensa.info  00000038  00000000  00000000  00000783  2**0
                  CONTENTS, READONLY
 19 .xt.lit       00000018  00000000  00000000  000007bb  2**0
                  CONTENTS, RELOC, READONLY
 20 .xt.prop      0000009c  00000000  00000000  000007d3  2**0
                  CONTENTS, RELOC, READONLY

xtensa_vector_defaults.S.obj:     file format elf32-xtensa-le

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .iram1.literal 00000004  00000000  00000000  00000034  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  1 .text         00000000  00000000  00000000  00000038  2**0
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  2 .data         00000000  00000000  00000000  00000038  2**0
                  CONTENTS, ALLOC, LOAD, DATA
  3 .bss          00000000  00000000  00000000  00000038  2**0
                  ALLOC
  4 .iram1        00000032  00000000  00000000  00000038  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  5 .xtensa.info  00000038  00000000  00000000  0000006a  2**0
                  CONTENTS, READONLY
  6 .debug_line   000000c2  00000000  00000000  000000a2  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
  7 .debug_info   000000a2  00000000  00000000  00000164  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
  8 .debug_abbrev 00000014  00000000  00000000  00000206  2**0
                  CONTENTS, READONLY, DEBUGGING
  9 .debug_aranges 00000020  00000000  00000000  00000220  2**3
                  CONTENTS, RELOC, READONLY, DEBUGGING
 10 .xt.lit       00000008  00000000  00000000  00000240  2**0
                  CONTENTS, RELOC, READONLY
 11 .xt.prop      00000078  00000000  00000000  00000248  2**0
                  CONTENTS, RELOC, READONLY

xtensa_vectors.S.obj:     file format elf32-xtensa-le

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .iram1.literal 000000a8  00000000  00000000  00000034  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  1 .DebugExceptionVector.literal 00000004  00000000  00000000  000000dc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  2 .DoubleExceptionVector.literal 00000004  00000000  00000000  000000e0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  3 .KernelExceptionVector.literal 00000004  00000000  00000000  000000e4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  4 .UserExceptionVector.literal 00000004  00000000  00000000  000000e8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  5 .Level2InterruptVector.literal 00000004  00000000  00000000  000000ec  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  6 .Level3InterruptVector.literal 00000004  00000000  00000000  000000f0  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  7 .Level4InterruptVector.literal 00000004  00000000  00000000  000000f4  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  8 .Level5InterruptVector.literal 00000004  00000000  00000000  000000f8  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
  9 .NMIExceptionVector.literal 00000004  00000000  00000000  000000fc  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 10 .text         00000000  00000000  00000000  00000100  2**0
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
 11 .data         00000008  00000000  00000000  00000100  2**4
                  CONTENTS, ALLOC, LOAD, DATA
 12 .bss          00000000  00000000  00000000  00000108  2**0
                  ALLOC
 13 .iram1        000004ec  00000000  00000000  00000108  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 14 .rodata       00000024  00000000  00000000  00000600  2**4
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
 15 .DebugExceptionVector.text 00000009  00000000  00000000  00000624  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 16 .DoubleExceptionVector.text 00000011  00000000  00000000  00000630  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 17 .KernelExceptionVector.text 00000009  00000000  00000000  00000644  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 18 .UserExceptionVector.text 00000009  00000000  00000000  00000650  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 19 .Level2InterruptVector.text 00000009  00000000  00000000  0000065c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 20 .Level3InterruptVector.text 00000009  00000000  00000000  00000668  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 21 .Level4InterruptVector.text 00000009  00000000  00000000  00000674  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 22 .Level5InterruptVector.text 00000009  00000000  00000000  00000680  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 23 .NMIExceptionVector.text 00000009  00000000  00000000  0000068c  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 24 .WindowVectors.text 0000016a  00000000  00000000  00000698  2**2
                  CONTENTS, ALLOC, LOAD, RELOC, READONLY, CODE
 25 .UserEnter.text 00000000  00000000  00000000  00000804  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
 26 .xtensa.info  00000038  00000000  00000000  00000804  2**0
                  CONTENTS, READONLY
 27 .debug_line   000009b8  00000000  00000000  0000083c  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 28 .debug_info   00000096  00000000  00000000  000011f4  2**0
                  CONTENTS, RELOC, READONLY, DEBUGGING
 29 .debug_abbrev 00000012  00000000  00000000  0000128a  2**0
                  CONTENTS, READONLY, DEBUGGING
 30 .debug_aranges 00000070  00000000  00000000  000012a0  2**3
                  CONTENTS, RELOC, READONLY, DEBUGGING
 31 .debug_ranges 00000068  00000000  00000000  00001310  2**3
                  CONTENTS, RELOC, READONLY, DEBUGGING
 32 .xt.lit       00000050  00000000  00000000  00001378  2**0
                  CONTENTS, RELOC, READONLY
 33 .xt.prop      000004f8  00000000  00000000  000013c8  2**0
                  CONTENTS, RELOC, READONLY

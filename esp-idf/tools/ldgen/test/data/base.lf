[sections:text]
entries:
    .text+
    .literal+

[sections:data]
entries:
    .data+

[sections:bss]
entries:
    .bss+

[sections:common]
entries:
    COMMON

[sections:rodata]
entries:
    .rodata+

[sections:rtc_text]
entries:
    .rtc.text
    .rtc.literal

[sections:rtc_data]
entries:
    .rtc.data

[sections:rtc_rodata]
entries:
    .rtc.rodata

[sections:rtc_bss]
entries:
    .rtc.bss

[sections:extram_bss]
entries:
    .exram.bss

[sections:iram]
entries:
    .iram+

[sections:dram]
entries:
    .dram+

[scheme:default]
entries:
    text -> flash_text
    rodata -> flash_rodata
    data -> dram0_data
    bss -> dram0_bss
    common -> dram0_bss
    iram -> iram0_text
    dram -> dram0_data
    rtc_text -> rtc_text
    rtc_data -> rtc_data
    rtc_rodata -> rtc_data
    rtc_bss -> rtc_bss

[scheme:rtc]
entries:
    text -> rtc_text
    data -> rtc_data
    rodata -> rtc_data
    bss -> rtc_bss
    common -> rtc_bss

[scheme:noflash]
entries:
    text -> iram0_text
    rodata -> dram0_data

[scheme:noflash_text]
entries:
    text -> iram0_text

[scheme:noflash_data]
entries:
    rodata -> dram0_data

[mapping:default]
archive: *
entries:
    * (default)

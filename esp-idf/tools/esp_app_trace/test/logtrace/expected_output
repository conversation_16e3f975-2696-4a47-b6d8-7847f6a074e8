Parse trace file 'adc_log.trc'...
Unprocessed 7 bytes of log record args!
Parsing completed.
====================================================================
[0;32mI (75854) example: Sample:1, Value:0[0m
[0;32mI (75854) example: Sample:2, Value:0[0m
[0;32mI (75854) example: Sample:3, Value:0[0m
[0;32mI (75854) example: Sample:4, Value:0[0m
[0;32mI (75854) example: Sample:5, Value:0[0m
[0;32mI (75854) example: Sample:6, Value:0[0m
[0;32mI (75854) example: Sample:7, Value:0[0m
[0;32mI (75854) example: Sample:8, Value:0[0m
[0;32mI (75854) example: Sample:9, Value:0[0m
[0;32mI (75854) example: Sample:10, Value:0[0m
[0;32mI (75854) example: Sample:11, Value:0[0m
[0;32mI (75854) example: Sample:12, Value:0[0m
[0;32mI (75854) example: Sample:13, Value:0[0m
[0;32mI (75854) example: Sample:14, Value:0[0m
[0;32mI (75854) example: Sample:15, Value:0[0m
[0;32mI (75854) example: Sample:16, Value:0[0m
[0;32mI (75854) example: Sample:17, Value:0[0m
[0;32mI (75854) example: Sample:18, Value:0[0m
[0;32mI (75854) example: Sample:19, Value:0[0m
[0;32mI (75854) example: Sample:20, Value:0[0m
[0;32mI (75854) example: Sample:21, Value:0[0m
[0;32mI (75854) example: Sample:22, Value:0[0m
[0;32mI (75854) example: Sample:23, Value:0[0m
[0;32mI (75854) example: Sample:24, Value:0[0m
[0;32mI (75854) example: Sample:25, Value:0[0m
[0;32mI (75854) example: Sample:26, Value:0[0m
[0;32mI (75854) example: Sample:27, Value:0[0m
[0;32mI (75854) example: Sample:28, Value:0[0m
[0;32mI (75854) example: Sample:29, Value:0[0m
[0;32mI (75854) example: Sample:30, Value:0[0m
[0;32mI (75854) example: Sample:31, Value:0[0m
[0;32mI (75854) example: Sample:32, Value:0[0m
[0;32mI (75854) example: Sample:33, Value:0[0m
[0;32mI (75854) example: Sample:34, Value:0[0m
[0;32mI (75854) example: Sample:35, Value:0[0m
[0;32mI (75854) example: Sample:36, Value:0[0m
[0;32mI (75854) example: Sample:37, Value:0[0m
[0;32mI (75854) example: Sample:38, Value:0[0m
[0;32mI (75854) example: Sample:39, Value:0[0m
[0;32mI (75854) example: Sample:40, Value:0[0m
[0;32mI (75854) example: Sample:41, Value:0[0m
[0;32mI (75854) example: Sample:42, Value:0[0m
[0;32mI (75854) example: Sample:43, Value:0[0m
[0;32mI (75854) example: Sample:44, Value:0[0m
[0;32mI (75854) example: Sample:45, Value:0[0m
[0;32mI (75854) example: Sample:46, Value:0[0m
[0;32mI (75854) example: Sample:47, Value:0[0m
[0;32mI (75854) example: Sample:48, Value:0[0m
[0;32mI (75854) example: Sample:49, Value:0[0m
[0;32mI (75854) example: Sample:50, Value:0[0m
[0;32mI (75854) example: Sample:51, Value:0[0m
[0;32mI (75854) example: Sample:52, Value:0[0m
[0;32mI (75854) example: Sample:53, Value:0[0m
[0;32mI (75854) example: Sample:54, Value:0[0m
[0;32mI (75854) example: Sample:55, Value:0[0m
[0;32mI (75854) example: Sample:56, Value:0[0m
[0;32mI (75854) example: Sample:57, Value:0[0m
[0;32mI (75854) example: Sample:58, Value:0[0m
[0;32mI (75854) example: Sample:59, Value:0[0m
[0;32mI (75854) example: Sample:60, Value:0[0m
[0;32mI (75854) example: Sample:61, Value:0[0m
[0;32mI (75854) example: Sample:62, Value:0[0m
[0;32mI (75854) example: Sample:63, Value:0[0m
[0;32mI (75854) example: Sample:64, Value:0[0m
[0;32mI (75854) example: Sample:65, Value:0[0m
[0;32mI (75854) example: Sample:66, Value:0[0m
[0;32mI (75854) example: Sample:67, Value:0[0m
[0;32mI (75854) example: Sample:68, Value:0[0m
[0;32mI (75854) example: Sample:69, Value:0[0m
[0;32mI (75854) example: Sample:70, Value:0[0m
[0;32mI (75854) example: Sample:71, Value:0[0m
[0;32mI (75854) example: Sample:72, Value:0[0m
[0;32mI (75854) example: Sample:73, Value:0[0m
[0;32mI (75854) example: Sample:74, Value:0[0m
[0;32mI (75854) example: Sample:75, Value:0[0m
[0;32mI (75854) example: Sample:76, Value:0[0m
[0;32mI (75854) example: Sample:77, Value:0[0m
[0;32mI (75854) example: Sample:78, Value:0[0m
[0;32mI (75854) example: Sample:79, Value:0[0m
[0;32mI (75854) example: Sample:80, Value:0[0m
[0;32mI (75854) example: Sample:81, Value:0[0m
[0;32mI (75854) example: Sample:82, Value:0[0m
[0;32mI (75854) example: Sample:83, Value:0[0m
[0;32mI (75854) example: Sample:84, Value:0[0m
[0;32mI (75854) example: Sample:85, Value:0[0m
[0;32mI (75854) example: Sample:86, Value:0[0m
[0;32mI (75854) example: Sample:87, Value:0[0m
[0;32mI (75854) example: Sample:88, Value:0[0m
[0;32mI (75854) example: Sample:89, Value:0[0m
[0;32mI (75854) example: Sample:90, Value:0[0m
[0;32mI (75854) example: Sample:91, Value:0[0m
[0;32mI (75854) example: Sample:92, Value:0[0m
[0;32mI (75854) example: Sample:93, Value:0[0m
[0;32mI (75854) example: Sample:94, Value:0[0m
[0;32mI (75854) example: Sample:95, Value:0[0m
[0;32mI (75854) example: Sample:96, Value:0[0m
[0;32mI (75854) example: Sample:97, Value:0[0m
[0;32mI (75854) example: Sample:98, Value:0[0m
[0;32mI (75854) example: Sample:99, Value:0[0m
[0;32mI (75854) example: Sample:100, Value:0[0m
[0;32mI (75854) example: Sample:101, Value:0[0m
[0;32mI (75854) example: Sample:102, Value:0[0m
[0;32mI (75854) example: Sample:103, Value:0[0m
[0;32mI (75854) example: Sample:104, Value:0[0m
[0;32mI (75854) example: Sample:105, Value:0[0m
[0;32mI (75854) example: Sample:106, Value:0[0m
[0;32mI (75854) example: Sample:107, Value:0[0m
[0;32mI (75854) example: Sample:108, Value:0[0m
[0;32mI (75854) example: Sample:109, Value:0[0m
[0;32mI (75854) example: Sample:110, Value:0[0m
[0;32mI (75854) example: Sample:111, Value:0[0m
[0;32mI (75854) example: Sample:112, Value:0[0m
[0;32mI (75854) example: Sample:113, Value:0[0m
[0;32mI (75854) example: Sample:114, Value:0[0m
[0;32mI (75854) example: Sample:115, Value:0[0m
[0;32mI (75854) example: Sample:116, Value:0[0m
[0;32mI (75854) example: Sample:117, Value:0[0m
[0;32mI (75854) example: Sample:118, Value:0[0m
[0;32mI (75854) example: Sample:119, Value:0[0m
[0;32mI (75854) example: Sample:120, Value:0[0m
[0;32mI (75854) example: Sample:121, Value:0[0m
[0;32mI (75854) example: Sample:122, Value:0[0m
[0;32mI (75854) example: Sample:123, Value:0[0m
[0;32mI (75854) example: Sample:124, Value:0[0m
[0;32mI (75854) example: Sample:125, Value:0[0m
[0;32mI (75854) example: Sample:126, Value:0[0m
[0;32mI (75854) example: Sample:127, Value:0[0m
[0;32mI (75854) example: Sample:128, Value:0[0m
[0;32mI (75854) example: Sample:129, Value:0[0m
[0;32mI (75854) example: Sample:130, Value:0[0m
[0;32mI (75854) example: Sample:131, Value:0[0m
[0;32mI (75854) example: Sample:132, Value:0[0m
[0;32mI (75854) example: Sample:133, Value:0[0m
[0;32mI (75854) example: Sample:134, Value:0[0m
[0;32mI (75854) example: Sample:135, Value:0[0m
[0;32mI (75854) example: Sample:136, Value:0[0m
[0;32mI (75854) example: Sample:137, Value:0[0m
[0;32mI (75854) example: Sample:138, Value:0[0m
[0;32mI (75854) example: Sample:139, Value:0[0m
[0;32mI (75854) example: Sample:140, Value:0[0m
[0;32mI (75854) example: Sample:141, Value:0[0m
[0;32mI (75854) example: Sample:142, Value:0[0m
[0;32mI (75854) example: Sample:143, Value:0[0m
[0;32mI (75854) example: Sample:144, Value:0[0m
[0;32mI (75854) example: Sample:145, Value:0[0m
[0;32mI (75854) example: Sample:146, Value:0[0m
[0;32mI (75854) example: Sample:147, Value:0[0m
[0;32mI (75854) example: Sample:148, Value:0[0m
[0;32mI (75854) example: Sample:149, Value:0[0m
[0;32mI (75854) example: Sample:150, Value:0[0m
[0;32mI (75854) example: Sample:151, Value:0[0m
[0;32mI (75854) example: Sample:152, Value:0[0m
[0;32mI (75854) example: Sample:153, Value:0[0m
[0;32mI (75854) example: Sample:154, Value:0[0m
[0;32mI (75854) example: Sample:155, Value:0[0m
[0;32mI (75854) example: Sample:156, Value:0[0m
[0;32mI (75854) example: Sample:157, Value:0[0m
[0;32mI (75854) example: Sample:158, Value:0[0m
[0;32mI (75854) example: Sample:159, Value:0[0m
[0;32mI (75854) example: Sample:160, Value:0[0m
[0;32mI (75854) example: Sample:161, Value:0[0m
[0;32mI (75854) example: Sample:162, Value:0[0m
[0;32mI (75854) example: Sample:163, Value:0[0m
[0;32mI (75854) example: Sample:164, Value:0[0m
[0;32mI (75854) example: Sample:165, Value:0[0m
[0;32mI (75854) example: Sample:166, Value:0[0m
[0;32mI (75854) example: Sample:167, Value:0[0m
[0;32mI (75854) example: Sample:168, Value:0[0m
[0;32mI (75854) example: Sample:169, Value:0[0m
[0;32mI (75854) example: Sample:170, Value:0[0m
[0;32mI (75854) example: Sample:171, Value:0[0m
[0;32mI (75854) example: Sample:172, Value:0[0m
[0;32mI (75854) example: Sample:173, Value:0[0m
[0;32mI (75854) example: Sample:174, Value:0[0m
[0;32mI (75854) example: Sample:175, Value:0[0m
[0;32mI (75854) example: Sample:176, Value:0[0m
[0;32mI (75864) example: Sample:177, Value:0[0m
[0;32mI (75864) example: Sample:178, Value:0[0m
[0;32mI (75864) example: Sample:179, Value:0[0m
[0;32mI (75864) example: Sample:180, Value:0[0m
[0;32mI (75864) example: Sample:181, Value:0[0m
[0;32mI (75864) example: Sample:182, Value:0[0m
[0;32mI (75864) example: Sample:183, Value:0[0m
[0;32mI (75864) example: Sample:184, Value:0[0m
[0;32mI (75864) example: Sample:185, Value:0[0m
[0;32mI (75864) example: Sample:186, Value:0[0m
[0;32mI (75864) example: Sample:187, Value:0[0m
[0;32mI (75864) example: Sample:188, Value:0[0m
[0;32mI (75864) example: Sample:189, Value:0[0m
[0;32mI (75864) example: Sample:190, Value:0[0m
[0;32mI (75864) example: Sample:191, Value:0[0m
[0;32mI (75864) example: Sample:192, Value:0[0m
[0;32mI (75864) example: Sample:193, Value:0[0m
[0;32mI (75864) example: Sample:194, Value:0[0m
[0;32mI (75864) example: Sample:195, Value:0[0m
[0;32mI (75864) example: Sample:196, Value:0[0m
[0;32mI (75864) example: Sample:197, Value:0[0m
[0;32mI (75864) example: Sample:198, Value:0[0m
[0;32mI (75864) example: Sample:199, Value:0[0m
[0;32mI (75864) example: Sample:200, Value:0[0m
[0;32mI (75864) example: Sample:201, Value:0[0m
[0;32mI (75864) example: Sample:202, Value:0[0m
[0;32mI (75864) example: Sample:203, Value:0[0m
[0;32mI (75864) example: Sample:204, Value:0[0m
[0;32mI (75864) example: Sample:205, Value:0[0m
[0;32mI (75864) example: Sample:206, Value:0[0m
[0;32mI (75864) example: Sample:207, Value:0[0m
[0;32mI (75864) example: Sample:208, Value:0[0m
[0;32mI (75864) example: Sample:209, Value:0[0m
[0;32mI (75864) example: Sample:210, Value:0[0m
[0;32mI (75864) example: Sample:211, Value:0[0m
[0;32mI (75864) example: Sample:212, Value:0[0m
[0;32mI (75864) example: Sample:213, Value:0[0m
[0;32mI (75864) example: Sample:214, Value:0[0m
[0;32mI (75864) example: Sample:215, Value:0[0m
[0;32mI (75864) example: Sample:216, Value:0[0m
[0;32mI (75864) example: Sample:217, Value:0[0m
[0;32mI (75864) example: Sample:218, Value:0[0m
[0;32mI (75864) example: Sample:219, Value:0[0m
[0;32mI (75864) example: Sample:220, Value:0[0m
[0;32mI (75864) example: Sample:221, Value:0[0m
[0;32mI (75864) example: Sample:222, Value:0[0m
[0;32mI (75864) example: Sample:223, Value:0[0m
[0;32mI (75864) example: Sample:224, Value:0[0m
[0;32mI (75864) example: Sample:225, Value:0[0m
[0;32mI (75864) example: Sample:226, Value:0[0m
[0;32mI (75864) example: Sample:227, Value:0[0m
[0;32mI (75864) example: Sample:228, Value:0[0m
[0;32mI (75864) example: Sample:229, Value:0[0m
[0;32mI (75864) example: Sample:230, Value:0[0m
[0;32mI (75864) example: Sample:231, Value:0[0m
[0;32mI (75864) example: Sample:232, Value:0[0m
[0;32mI (75864) example: Sample:233, Value:0[0m
[0;32mI (75864) example: Sample:234, Value:0[0m
[0;32mI (75864) example: Sample:235, Value:0[0m
[0;32mI (75864) example: Sample:236, Value:0[0m
[0;32mI (75864) example: Sample:237, Value:0[0m
[0;32mI (75864) example: Sample:238, Value:0[0m
[0;32mI (75864) example: Sample:239, Value:0[0m
[0;32mI (75864) example: Sample:240, Value:0[0m
[0;32mI (75864) example: Sample:241, Value:0[0m
[0;32mI (75864) example: Sample:242, Value:0[0m
[0;32mI (75864) example: Sample:243, Value:0[0m
[0;32mI (75864) example: Sample:244, Value:0[0m
[0;32mI (75864) example: Sample:245, Value:0[0m
[0;32mI (75864) example: Sample:246, Value:0[0m
[0;32mI (75864) example: Sample:247, Value:0[0m
[0;32mI (75864) example: Sample:248, Value:0[0m
[0;32mI (75864) example: Sample:249, Value:0[0m
[0;32mI (75864) example: Sample:250, Value:0[0m
[0;32mI (75864) example: Sample:251, Value:0[0m
[0;32mI (75864) example: Sample:252, Value:0[0m
[0;32mI (75864) example: Sample:253, Value:0[0m
[0;32mI (75864) example: Sample:254, Value:0[0m
[0;32mI (75864) example: Sample:255, Value:0[0m
[0;32mI (75864) example: Sample:256, Value:0[0m
[0;32mI (75864) example: Sample:257, Value:0[0m
[0;32mI (75864) example: Sample:258, Value:0[0m
[0;32mI (75864) example: Sample:259, Value:0[0m
[0;32mI (75864) example: Sample:260, Value:0[0m
[0;32mI (75864) example: Sample:261, Value:0[0m
[0;32mI (75864) example: Sample:262, Value:0[0m
[0;32mI (75864) example: Sample:263, Value:0[0m
[0;32mI (75864) example: Sample:264, Value:0[0m
[0;32mI (75864) example: Sample:265, Value:0[0m
[0;32mI (75864) example: Sample:266, Value:0[0m
[0;32mI (75864) example: Sample:267, Value:0[0m
[0;32mI (75864) example: Sample:268, Value:0[0m
[0;32mI (75864) example: Sample:269, Value:0[0m
[0;32mI (75864) example: Sample:270, Value:0[0m
[0;32mI (75864) example: Sample:271, Value:0[0m
[0;32mI (75864) example: Sample:272, Value:0[0m
[0;32mI (75864) example: Sample:273, Value:0[0m
[0;32mI (75864) example: Sample:274, Value:0[0m
[0;32mI (75864) example: Sample:275, Value:0[0m
[0;32mI (75864) example: Sample:276, Value:0[0m
[0;32mI (75864) example: Sample:277, Value:0[0m
[0;32mI (75864) example: Sample:278, Value:0[0m
[0;32mI (75864) example: Sample:279, Value:0[0m
[0;32mI (75864) example: Sample:280, Value:0[0m
[0;32mI (75864) example: Sample:281, Value:0[0m
[0;32mI (75864) example: Sample:282, Value:0[0m
[0;32mI (75864) example: Sample:283, Value:0[0m
[0;32mI (75864) example: Sample:284, Value:0[0m
[0;32mI (75864) example: Sample:285, Value:0[0m
[0;32mI (75864) example: Sample:286, Value:0[0m
[0;32mI (75864) example: Sample:287, Value:0[0m
[0;32mI (75864) example: Sample:288, Value:0[0m
[0;32mI (75864) example: Sample:289, Value:0[0m
[0;32mI (75864) example: Sample:290, Value:0[0m
[0;32mI (75864) example: Sample:291, Value:0[0m
[0;32mI (75864) example: Sample:292, Value:0[0m
[0;32mI (75864) example: Sample:293, Value:0[0m
[0;32mI (75864) example: Sample:294, Value:0[0m
[0;32mI (75864) example: Sample:295, Value:0[0m
[0;32mI (75864) example: Sample:296, Value:0[0m
[0;32mI (75864) example: Sample:297, Value:0[0m
[0;32mI (75864) example: Sample:298, Value:0[0m
[0;32mI (75864) example: Sample:299, Value:0[0m
[0;32mI (75864) example: Sample:300, Value:0[0m
[0;32mI (75864) example: Sample:301, Value:0[0m
[0;32mI (75864) example: Sample:302, Value:0[0m
[0;32mI (75864) example: Sample:303, Value:0[0m
[0;32mI (75864) example: Sample:304, Value:0[0m
[0;32mI (75864) example: Sample:305, Value:0[0m
[0;32mI (75864) example: Sample:306, Value:0[0m
[0;32mI (75864) example: Sample:307, Value:0[0m
[0;32mI (75864) example: Sample:308, Value:0[0m
[0;32mI (75864) example: Sample:309, Value:0[0m
[0;32mI (75864) example: Sample:310, Value:0[0m
[0;32mI (75864) example: Sample:311, Value:0[0m
[0;32mI (75864) example: Sample:312, Value:0[0m
[0;32mI (75864) example: Sample:313, Value:0[0m
[0;32mI (75864) example: Sample:314, Value:0[0m
[0;32mI (75864) example: Sample:315, Value:0[0m
[0;32mI (75864) example: Sample:316, Value:0[0m
[0;32mI (75864) example: Sample:317, Value:0[0m
[0;32mI (75864) example: Sample:318, Value:0[0m
[0;32mI (75864) example: Sample:319, Value:0[0m
[0;32mI (75864) example: Sample:320, Value:0[0m
[0;32mI (75864) example: Sample:321, Value:0[0m
[0;32mI (75864) example: Sample:322, Value:0[0m
[0;32mI (75864) example: Sample:323, Value:0[0m
[0;32mI (75864) example: Sample:324, Value:0[0m
[0;32mI (75864) example: Sample:325, Value:0[0m
[0;32mI (75864) example: Sample:326, Value:0[0m
[0;32mI (75864) example: Sample:327, Value:0[0m
[0;32mI (75864) example: Sample:328, Value:0[0m
[0;32mI (75864) example: Sample:329, Value:0[0m
[0;32mI (75864) example: Sample:330, Value:0[0m
[0;32mI (75864) example: Sample:331, Value:0[0m
[0;32mI (75864) example: Sample:332, Value:0[0m
[0;32mI (75864) example: Sample:333, Value:0[0m
[0;32mI (75864) example: Sample:334, Value:0[0m
[0;32mI (75864) example: Sample:335, Value:0[0m
[0;32mI (75864) example: Sample:336, Value:0[0m
[0;32mI (75864) example: Sample:337, Value:0[0m
[0;32mI (75864) example: Sample:338, Value:0[0m
[0;32mI (75864) example: Sample:339, Value:0[0m
[0;32mI (75864) example: Sample:340, Value:0[0m
[0;32mI (75864) example: Sample:341, Value:0[0m
[0;32mI (75864) example: Sample:342, Value:0[0m
[0;32mI (75864) example: Sample:343, Value:0[0m
[0;32mI (75864) example: Sample:344, Value:0[0m
[0;32mI (75864) example: Sample:345, Value:0[0m
[0;32mI (75864) example: Sample:346, Value:0[0m
[0;32mI (75864) example: Sample:347, Value:0[0m
[0;32mI (75864) example: Sample:348, Value:0[0m
[0;32mI (75864) example: Sample:349, Value:0[0m
[0;32mI (75864) example: Sample:350, Value:0[0m
[0;32mI (75864) example: Sample:351, Value:0[0m
[0;32mI (75864) example: Sample:352, Value:0[0m
[0;32mI (75864) example: Sample:353, Value:0[0m
[0;32mI (77894) example: Sample:1, Value:0[0m
[0;32mI (77894) example: Sample:2, Value:0[0m
[0;32mI (77894) example: Sample:3, Value:0[0m
[0;32mI (77894) example: Sample:4, Value:0[0m
[0;32mI (77894) example: Sample:5, Value:0[0m
[0;32mI (77894) example: Sample:6, Value:0[0m
[0;32mI (77894) example: Sample:7, Value:0[0m
[0;32mI (77894) example: Sample:8, Value:0[0m
[0;32mI (77894) example: Sample:9, Value:0[0m
[0;32mI (77894) example: Sample:10, Value:0[0m
[0;32mI (77894) example: Sample:11, Value:0[0m
[0;32mI (77894) example: Sample:12, Value:0[0m
[0;32mI (77894) example: Sample:13, Value:0[0m
[0;32mI (77894) example: Sample:14, Value:0[0m
[0;32mI (77894) example: Sample:15, Value:0[0m
[0;32mI (77894) example: Sample:16, Value:0[0m
[0;32mI (77894) example: Sample:17, Value:0[0m
[0;32mI (77894) example: Sample:18, Value:0[0m
[0;32mI (77894) example: Sample:19, Value:0[0m
[0;32mI (77894) example: Sample:20, Value:0[0m
[0;32mI (77894) example: Sample:21, Value:0[0m
[0;32mI (77894) example: Sample:22, Value:0[0m
[0;32mI (77894) example: Sample:23, Value:0[0m
[0;32mI (77894) example: Sample:24, Value:0[0m
[0;32mI (77894) example: Sample:25, Value:0[0m
[0;32mI (77894) example: Sample:26, Value:0[0m
[0;32mI (77894) example: Sample:27, Value:0[0m
[0;32mI (77894) example: Sample:28, Value:0[0m
[0;32mI (77894) example: Sample:29, Value:0[0m
[0;32mI (77894) example: Sample:30, Value:0[0m
[0;32mI (77894) example: Sample:31, Value:0[0m
[0;32mI (77894) example: Sample:32, Value:0[0m
[0;32mI (77894) example: Sample:33, Value:0[0m
[0;32mI (77894) example: Sample:34, Value:0[0m
[0;32mI (77894) example: Sample:35, Value:0[0m
[0;32mI (77894) example: Sample:36, Value:0[0m
[0;32mI (77894) example: Sample:37, Value:0[0m
[0;32mI (77894) example: Sample:38, Value:0[0m
[0;32mI (77894) example: Sample:39, Value:0[0m
[0;32mI (77894) example: Sample:40, Value:0[0m
[0;32mI (77894) example: Sample:41, Value:0[0m
[0;32mI (77894) example: Sample:42, Value:0[0m
[0;32mI (77894) example: Sample:43, Value:0[0m
[0;32mI (77894) example: Sample:44, Value:0[0m
[0;32mI (77894) example: Sample:45, Value:0[0m
[0;32mI (77894) example: Sample:46, Value:0[0m
[0;32mI (77894) example: Sample:47, Value:0[0m
[0;32mI (77894) example: Sample:48, Value:0[0m
[0;32mI (77894) example: Sample:49, Value:0[0m
[0;32mI (77894) example: Sample:50, Value:0[0m
[0;32mI (77894) example: Sample:51, Value:0[0m
[0;32mI (77894) example: Sample:52, Value:0[0m
[0;32mI (77894) example: Sample:53, Value:0[0m
[0;32mI (77894) example: Sample:54, Value:0[0m
[0;32mI (77894) example: Sample:55, Value:0[0m
[0;32mI (77894) example: Sample:56, Value:0[0m
[0;32mI (77894) example: Sample:57, Value:0[0m
[0;32mI (77894) example: Sample:58, Value:0[0m
[0;32mI (77894) example: Sample:59, Value:0[0m
[0;32mI (77894) example: Sample:60, Value:0[0m
[0;32mI (77894) example: Sample:61, Value:0[0m
[0;32mI (77894) example: Sample:62, Value:0[0m
[0;32mI (77894) example: Sample:63, Value:0[0m
[0;32mI (77894) example: Sample:64, Value:0[0m
[0;32mI (77894) example: Sample:65, Value:0[0m
[0;32mI (77894) example: Sample:66, Value:0[0m
[0;32mI (77894) example: Sample:67, Value:0[0m
[0;32mI (77894) example: Sample:68, Value:0[0m
[0;32mI (77894) example: Sample:69, Value:0[0m
[0;32mI (77894) example: Sample:70, Value:0[0m
[0;32mI (77894) example: Sample:71, Value:0[0m
[0;32mI (77894) example: Sample:72, Value:0[0m
[0;32mI (77894) example: Sample:73, Value:0[0m
[0;32mI (77894) example: Sample:74, Value:0[0m
[0;32mI (77894) example: Sample:75, Value:0[0m

====================================================================

Log records count: 428

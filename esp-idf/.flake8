[flake8]

select =
    # Full lists are given in order to suppress all errors from other plugins
    # Full list of pyflakes error codes:
    F401,           # module imported but unused
    F402,           # import module from line N shadowed by loop variable
    F403,           # 'from module import *' used; unable to detect undefined names
    F404,           # future import(s) name after other statements
    F405,           # name may be undefined, or defined from star imports: module
    F406,           # 'from module import *' only allowed at module level
    F407,           # an undefined __future__ feature name was imported
    F601,           # dictionary key name repeated with different values
    F602,           # dictionary key variable name repeated with different values
    F621,           # too many expressions in an assignment with star-unpacking
    F622,           # two or more starred expressions in an assignment (a, *b, *c = d)
    F631,           # assertion test is a tuple, which are always True
    F701,           # a break statement outside of a while or for loop
    F702,           # a continue statement outside of a while or for loop
    F703,           # a continue statement in a finally block in a loop
    F704,           # a yield or yield from statement outside of a function
    F705,           # a return statement with arguments inside a generator
    F706,           # a return statement outside of a function/method
    F707,           # an except: block as not the last exception handler
    F721, F722,     #    doctest syntax error syntax error in forward type annotation
    F811,           # redefinition of unused name from line N
    F812,           # list comprehension redefines name from line N
    F821,           # undefined name name
    F822,           # undefined name name in __all__
    F823,           # local variable name referenced before assignment
    F831,           # duplicate argument name in function definition
    F841,           # local variable name is assigned to but never used
    F901,           # raise NotImplemented should be raise NotImplementedError

    # Full list of pycodestyle violations:
    E101,           # indentation contains mixed spaces and tabs
    E111,           # indentation is not a multiple of four
    E112,           # expected an indented block
    E113,           # unexpected indentation
    E114,           # indentation is not a multiple of four (comment)
    E115,           # expected an indented block (comment)
    E116,           # unexpected indentation (comment)
    E121,           # continuation line under-indented for hanging indent
    E122,           # continuation line missing indentation or outdented
    E123,           # closing bracket does not match indentation of opening bracket's line
    E124,           # closing bracket does not match visual indentation
    E125,           # continuation line with same indent as next logical line
    E126,           # continuation line over-indented for hanging indent
    E127,           # continuation line over-indented for visual indent
    E128,           # continuation line under-indented for visual indent
    E129,           # visually indented line with same indent as next logical line
    E131,           # continuation line unaligned for hanging indent
    E133,           # closing bracket is missing indentation
    E201,           # whitespace after '('
    E202,           # whitespace before ')'
    E203,           # whitespace before ':'
    E211,           # whitespace before '('
    E221,           # multiple spaces before operator
    E222,           # multiple spaces after operator
    E223,           # tab before operator
    E224,           # tab after operator
    E225,           # missing whitespace around operator
    E226,           # missing whitespace around arithmetic operator
    E227,           # missing whitespace around bitwise or shift operator
    E228,           # missing whitespace around modulo operator
    E231,           # missing whitespace after ',', ';', or ':'
    E241,           # multiple spaces after ','
    E242,           # tab after ','
    E251,           # unexpected spaces around keyword / parameter equals
    E261,           # at least two spaces before inline comment
    E262,           # inline comment should start with '# '
    E265,           # block comment should start with '# '
    E266,           # too many leading '#' for block comment
    E271,           # multiple spaces after keyword
    E272,           # multiple spaces before keyword
    E273,           # tab after keyword
    E274,           # tab before keyword
    E275,           # missing whitespace after keyword
    E301,           # expected 1 blank line, found 0
    E302,           # expected 2 blank lines, found 0
    E303,           # too many blank lines
    E304,           # blank lines found after function decorator
    E305,           # expected 2 blank lines after end of function or class
    E306,           # expected 1 blank line before a nested definition
    E401,           # multiple imports on one line
    E402,           # module level import not at top of file
    E501,           # line too long (82 > 79 characters)
    E502,           # the backslash is redundant between brackets
    E701,           # multiple statements on one line (colon)
    E702,           # multiple statements on one line (semicolon)
    E703,           # statement ends with a semicolon
    E704,           # multiple statements on one line (def)
    E711,           # comparison to None should be 'if cond is None:'
    E712,           # comparison to True should be 'if cond is True:' or 'if cond:'
    E713,           # test for membership should be 'not in'
    E714,           # test for object identity should be 'is not'
    E721,           # do not compare types, use 'isinstance()'
    E722,           # do not use bare except, specify exception instead
    E731,           # do not assign a lambda expression, use a def
    E741,           # do not use variables named 'l', 'O', or 'I'
    E742,           # do not define classes named 'l', 'O', or 'I'
    E743,           # do not define functions named 'l', 'O', or 'I'
    E901,           # SyntaxError or IndentationError
    E902,           # IOError
    W191,           # indentation contains tabs
    W291,           # trailing whitespace
    W292,           # no newline at end of file
    W293,           # blank line contains whitespace
    W391,           # blank line at end of file
    W503,           # line break before binary operator
    W504,           # line break after binary operator
    W505,           # doc line too long (82 > 79 characters)
    W601,           # .has_key() is deprecated, use 'in'
    W602,           # deprecated form of raising exception
    W603,           # '<>' is deprecated, use '!='
    W604,           # backticks are deprecated, use 'repr()'
    W605,           # invalid escape sequence 'x'
    W606,           # 'async' and 'await' are reserved keywords starting with Python 3.7

    # Full list of flake8 violations
    E999,           # failed to compile a file into an Abstract Syntax Tree for the plugins that require it

    # Full list of mccabe violations
    C901            # complexity value provided by the user

ignore =
    E221,           # multiple spaces before operator
    E231,           # missing whitespace after ',', ';', or ':'
    E241,           # multiple spaces after ','
    W503,           # line break before binary operator
    W504            # line break after binary operator

max-line-length = 160

show_source = True

statistics = True

exclude =
    .git,
    __pycache__,
    # submodules
        components/bootloader/subproject/components/micro-ecc/micro-ecc,
        components/bt/host/nimble/nimble,
        components/cmock/CMock,
        components/json/cJSON,
        components/mbedtls/mbedtls,
        components/openthread/openthread,
        components/unity/unity,
        components/spiffs/spiffs,
    # autogenerated scripts
        components/protocomm/python/constants_pb2.py,
        components/protocomm/python/sec0_pb2.py,
        components/protocomm/python/sec1_pb2.py,
        components/protocomm/python/sec2_pb2.py,
        components/protocomm/python/session_pb2.py,
        components/wifi_provisioning/python/wifi_ctrl_pb2.py,
        components/wifi_provisioning/python/wifi_scan_pb2.py,
        components/wifi_provisioning/python/wifi_config_pb2.py,
        components/wifi_provisioning/python/wifi_constants_pb2.py,
        components/esp_local_ctrl/python/esp_local_ctrl_pb2.py,

per-file-ignores =
    # Sphinx conf.py files use star imports to setup config variables
        docs/conf_common.py: F405

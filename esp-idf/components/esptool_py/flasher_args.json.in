{
    "write_flash_args" : [ "--flash_mode", "${<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}",
                           "--flash_size", "${ESPFLASHSIZE}",
                           "--flash_freq", "${ESPFLASHFREQ}" ],
    "flash_settings" : {
        "flash_mode": "${<PERSON><PERSON><PERSON><PERSON>HMODE}",
        "flash_size": "${ESPFLASHSIZE}",
        "flash_freq": "${ESPFLASHFREQ}"
    },
    "flash_files" : {
        $<JOIN:$<TARGET_PROPERTY:flash,FLASH_FILE>,,
        >
    },
    $<JOIN:$<TARGET_PROPERTY:flash,FLASH_ENTRY>,,
    >$<$<BOOL:$<TARGET_PROPERTY:flash,FLASH_ENTRY>>:,>
    "extra_esptool_args" : {
        "after"  : "${ESPTOOLPY_AFTER}",
        "before" : "${ESPTOOLPY_BEFORE}",
        "stub"   : ${ESPTOOLPY_WITH_STUB},
        "chip"   : "${ESPTOOLPY_CHIP}"
    }
}

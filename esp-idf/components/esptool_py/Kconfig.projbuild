menu "Serial flasher config"
    depends on !APP_BUILD_TYPE_PURE_RAM_APP

    config ESPTOOLPY_NO_STUB
        bool "Disable download stub"
        default "y" if IDF_ENV_FPGA || IDF_ENV_BRINGUP
        default "n"

        help
            The flasher tool sends a precompiled download stub first by default. That stub allows things
            like compressed downloads and more. Usually you should not need to disable that feature

    config ESPTOOLPY_OCT_FLASH
        depends on IDF_TARGET_ESP32S3
        bool "Enable Octal Flash"
        default n

    config ESPTOOLPY_FLASH_MODE_AUTO_DETECT
        depends on IDF_TARGET_ESP32S3
        bool "Choose flash mode automatically (please read help)"
        default y
        help
            This config option helps decide whether flash is Quad or Octal, but please note some limitations:

            1. If the flash chip is an Octal one, even if one of "QIO", "QOUT", "DIO", "DOUT" options is
               selected in `ESPTOOLPY_FLASHMODE`, our code will automatically change the
               mode to "OPI" and the sample mode will be STR.
            2. If the flash chip is a Quad one, even if "OPI" is selected in `ESPTOOLPY_FLASHMODE`, our code will
               automatically change the mode to "DIO".
            3. This option is mainly to improve the out-of-box experience of developers. It doesn't guarantee
               the feature-complete. Some code still rely on `ESPTOOLPY_OCT_FLASH`. Please do not rely on this option
               when you are pretty sure that you are using Octal flash.
               In this case, please enable `ESPTOOLPY_OCT_FLASH` option, then you can choose `DTR` sample mode
               in `ESPTOOLPY_FLASH_SAMPLE_MODE`. Otherwise, only `STR` mode is available.
            4. Enabling this feature reduces available internal RAM size (around 900 bytes).
               If your IRAM space is insufficient and you're aware of your flash type,
               disable this option and select corresponding flash type options.

    choice ESPTOOLPY_FLASHMODE
        prompt "Flash SPI mode"
        default ESPTOOLPY_FLASHMODE_DIO
        default ESPTOOLPY_FLASHMODE_OPI if ESPTOOLPY_OCT_FLASH
        help
            Mode the flash chip is flashed in, as well as the default mode for the
            binary to run in.

        config ESPTOOLPY_FLASHMODE_QIO
            depends on !ESPTOOLPY_OCT_FLASH
            bool "QIO"
        config ESPTOOLPY_FLASHMODE_QOUT
            depends on !ESPTOOLPY_OCT_FLASH
            bool "QOUT"
        config ESPTOOLPY_FLASHMODE_DIO
            depends on !ESPTOOLPY_OCT_FLASH
            bool "DIO"
        config ESPTOOLPY_FLASHMODE_DOUT
            depends on !ESPTOOLPY_OCT_FLASH
            bool "DOUT"
        config ESPTOOLPY_FLASHMODE_OPI
            depends on ESPTOOLPY_OCT_FLASH
            bool "OPI"
    endchoice

    choice ESPTOOLPY_FLASH_SAMPLE_MODE
        prompt "Flash Sampling Mode"
        default ESPTOOLPY_FLASH_SAMPLE_MODE_DTR if ESPTOOLPY_OCT_FLASH
        default ESPTOOLPY_FLASH_SAMPLE_MODE_STR if !ESPTOOLPY_OCT_FLASH

        config ESPTOOLPY_FLASH_SAMPLE_MODE_STR
            bool "STR Mode"
        config ESPTOOLPY_FLASH_SAMPLE_MODE_DTR
            depends on ESPTOOLPY_OCT_FLASH
            bool "DTR Mode"
    endchoice

    # Note: we use esptool.py to flash bootloader in
    # dio mode for QIO/QOUT, bootloader then upgrades
    # itself to quad mode during initialisation
    config ESPTOOLPY_FLASHMODE
        string
        default "dio" if ESPTOOLPY_FLASHMODE_QIO
        default "dio" if ESPTOOLPY_FLASHMODE_QOUT
        default "dio" if ESPTOOLPY_FLASHMODE_DIO
        default "dout" if ESPTOOLPY_FLASHMODE_DOUT
        # The 1st and 2nd bootloader doesn't support opi mode,
        # using fastrd instead. For now the ESPTOOL doesn't support
        # fasted (see ESPTOOL-274), using dout instead. In ROM the flash mode
        # information get from efuse, so don't care this dout choice.
        default "dout" if ESPTOOLPY_FLASHMODE_OPI

    choice ESPTOOLPY_FLASHFREQ
        prompt "Flash SPI speed"
        # TODO: [ESP32C5] IDF-8649 switch back to 80M
        # TODO: [ESP32C61] IDF-9256
        default ESPTOOLPY_FLASHFREQ_40M if IDF_TARGET_ESP32 || IDF_TARGET_ESP32C5 || IDF_TARGET_ESP32C61
        default ESPTOOLPY_FLASHFREQ_80M if ESPTOOLPY_FLASHFREQ_80M_DEFAULT
        default ESPTOOLPY_FLASHFREQ_60M if IDF_TARGET_ESP32C2
        config ESPTOOLPY_FLASHFREQ_120M
            bool "120 MHz (READ DOCS FIRST)"
            depends on SOC_MEMSPI_SRC_FREQ_120M && \
                (SPI_FLASH_HPM_ON || ESPTOOLPY_OCT_FLASH) && \
                (ESPTOOLPY_FLASH_SAMPLE_MODE_STR || IDF_EXPERIMENTAL_FEATURES)
            help
                - Optional feature for QSPI Flash. Read docs and enable `CONFIG_SPI_FLASH_HPM_ENA` first!
                - Flash 120 MHz SDR mode is stable.
                - Flash 120 MHz DDR mode is an experimental feature, it works when
                  the temperature is stable.

                    Risks:
                        If your chip powers on at a certain temperature, then after the temperature
                        increases or decreases by approximately 20 Celsius degrees (depending on the
                        chip), the program will crash randomly.

        config ESPTOOLPY_FLASHFREQ_80M
            bool "80 MHz"
            depends on SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED
        config ESPTOOLPY_FLASHFREQ_64M
            bool "64 MHz"
            depends on SOC_MEMSPI_SRC_FREQ_64M_SUPPORTED
        config ESPTOOLPY_FLASHFREQ_60M
            bool "60 MHz"
            depends on SOC_MEMSPI_SRC_FREQ_60M_SUPPORTED
        config ESPTOOLPY_FLASHFREQ_48M
            bool "48 MHz"
            depends on SOC_MEMSPI_SRC_FREQ_48M_SUPPORTED
        config ESPTOOLPY_FLASHFREQ_40M
            bool "40 MHz"
            depends on SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED
        config ESPTOOLPY_FLASHFREQ_32M
            bool "32 MHz"
            depends on SOC_MEMSPI_SRC_FREQ_32M_SUPPORTED
        config ESPTOOLPY_FLASHFREQ_30M
            bool "30 MHz"
            depends on SOC_MEMSPI_SRC_FREQ_30M_SUPPORTED
        config ESPTOOLPY_FLASHFREQ_26M
            bool "26 MHz"
            depends on SOC_MEMSPI_SRC_FREQ_26M_SUPPORTED
        config ESPTOOLPY_FLASHFREQ_24M
            bool "24 MHz"
            depends on SOC_MEMSPI_SRC_FREQ_24M_SUPPORTED
        config ESPTOOLPY_FLASHFREQ_20M
            bool "20 MHz"
            depends on SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED
        config ESPTOOLPY_FLASHFREQ_16M
            bool "16 MHz"
            depends on SOC_MEMSPI_SRC_FREQ_16M_SUPPORTED
        config ESPTOOLPY_FLASHFREQ_15M
            bool "15 MHz"
            depends on SOC_MEMSPI_SRC_FREQ_15M_SUPPORTED
    endchoice

    config ESPTOOLPY_FLASHFREQ_80M_DEFAULT
        bool
        default y if IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32S3 || IDF_TARGET_ESP32C3 || IDF_TARGET_ESP32C6
        help
            This is an invisible item, used to define the targets that defaults to use 80MHz Flash SPI speed.

    config ESPTOOLPY_FLASHFREQ
        string
        # On some of the ESP chips, max boot frequency would be equal to (or even lower than) 80m.
        # We currently define this to `80m`.
        default '80m' if ESPTOOLPY_FLASHFREQ_120M
        default '80m' if ESPTOOLPY_FLASHFREQ_80M
        default '60m' if ESPTOOLPY_FLASHFREQ_60M
        default '48m' if ESPTOOLPY_FLASHFREQ_64M # For 0xf in esptool
        default '48m' if ESPTOOLPY_FLASHFREQ_48M
        default '24m' if ESPTOOLPY_FLASHFREQ_32M # For 0x0 in esptool
        default '30m' if ESPTOOLPY_FLASHFREQ_30M
        default '24m' if ESPTOOLPY_FLASHFREQ_24M
        default '40m' if ESPTOOLPY_FLASHFREQ_40M
        default '26m' if ESPTOOLPY_FLASHFREQ_26M
        default '20m' if ESPTOOLPY_FLASHFREQ_20M
        default '12m' if ESPTOOLPY_FLASHFREQ_16M # For 0x2 in esptool
        default '20m' # if no clock can match in bin headers, go with minimal.


    choice ESPTOOLPY_FLASHSIZE
        prompt "Flash size"
        default ESPTOOLPY_FLASHSIZE_2MB
        help
            SPI flash size, in megabytes

        config ESPTOOLPY_FLASHSIZE_1MB
            bool "1 MB"
        config ESPTOOLPY_FLASHSIZE_2MB
            bool "2 MB"
        config ESPTOOLPY_FLASHSIZE_4MB
            bool "4 MB"
        config ESPTOOLPY_FLASHSIZE_8MB
            bool "8 MB"
        config ESPTOOLPY_FLASHSIZE_16MB
            bool "16 MB"
        config ESPTOOLPY_FLASHSIZE_32MB
            bool "32 MB"
        config ESPTOOLPY_FLASHSIZE_64MB
            bool "64 MB"
        config ESPTOOLPY_FLASHSIZE_128MB
            bool "128 MB"
    endchoice

    config ESPTOOLPY_FLASHSIZE
        string
        default "1MB" if ESPTOOLPY_FLASHSIZE_1MB
        default "2MB" if ESPTOOLPY_FLASHSIZE_2MB
        default "4MB" if ESPTOOLPY_FLASHSIZE_4MB
        default "8MB" if ESPTOOLPY_FLASHSIZE_8MB
        default "16MB" if ESPTOOLPY_FLASHSIZE_16MB
        default "32MB" if ESPTOOLPY_FLASHSIZE_32MB
        default "64MB" if ESPTOOLPY_FLASHSIZE_64MB
        default "128MB" if ESPTOOLPY_FLASHSIZE_128MB

    config ESPTOOLPY_HEADER_FLASHSIZE_UPDATE
        bool "Detect flash size when flashing bootloader"
        default n
        help
            If this option is set, flashing the project will automatically detect
            the flash size of the target chip and update the bootloader image
            before it is flashed.

            Enabling this option turns off the image protection against corruption
            by a SHA256 digest. Updating the bootloader image before flashing would
            invalidate the digest.

    choice ESPTOOLPY_BEFORE
        prompt "Before flashing"
        default ESPTOOLPY_BEFORE_RESET
        help
            Configure whether esptool.py should reset the ESP32 before flashing.

            Automatic resetting depends on the RTS & DTR signals being
            wired from the serial port to the ESP32. Most USB development
            boards do this internally.

        config ESPTOOLPY_BEFORE_RESET
            bool "Reset to bootloader"
        config ESPTOOLPY_BEFORE_NORESET
            bool "No reset"
    endchoice

    config ESPTOOLPY_BEFORE
        string
        default "default_reset" if ESPTOOLPY_BEFORE_RESET
        default "no_reset" if ESPTOOLPY_BEFORE_NORESET

    choice ESPTOOLPY_AFTER
        prompt "After flashing"
        default ESPTOOLPY_AFTER_RESET
        help
            Configure whether esptool.py should reset the ESP32 after flashing.

            Automatic resetting depends on the RTS & DTR signals being
            wired from the serial port to the ESP32. Most USB development
            boards do this internally.

        config ESPTOOLPY_AFTER_RESET
            bool "Reset after flashing"
        config ESPTOOLPY_AFTER_NORESET
            bool "Stay in bootloader"
    endchoice

    config ESPTOOLPY_AFTER
        string
        default "hard_reset" if ESPTOOLPY_AFTER_RESET
        default "no_reset" if ESPTOOLPY_AFTER_NORESET

    config ESPTOOLPY_MONITOR_BAUD
        int
        default ESP_CONSOLE_UART_BAUDRATE if ESP_CONSOLE_UART
        default 115200 if !ESP_CONSOLE_UART
endmenu

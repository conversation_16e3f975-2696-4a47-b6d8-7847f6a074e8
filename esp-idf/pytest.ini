[pytest]
# only the files with prefix `pytest_` would be recognized as pytest test scripts.
python_files = pytest_*.py

# ignore PytestExperimentalApiWarning for record_xml_attribute
# set traceback to "short" to prevent the overwhelming tracebacks
addopts =
  -s -vv
  --embedded-services esp,idf
  --tb short
  --strict-markers
  --skip-check-coredump y
  --logfile-extension ".txt"
  --check-duplicates y
  --ignore-glob */managed_components/*
  --ignore pytest-embedded

# ignore DeprecationWarning
filterwarnings =
  ignore::DeprecationWarning:matplotlib.*:
  ignore::DeprecationWarning:google.protobuf.*:
  ignore::_pytest.warning_types.PytestExperimentalApiWarning

# log related
log_cli = True
log_cli_level = INFO
log_cli_format = %(asctime)s %(levelname)s %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# junit related
junit_family = xunit1

## log all to `system-out` when case fail
junit_logging = stdout
junit_log_passing_tests = False

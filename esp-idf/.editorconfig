# EditorConfig helps developers define and maintain consistent
# coding styles between different editors and IDEs
# http://editorconfig.org

root = true

[*]
indent_style = space
indent_size = 4
end_of_line = lf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true

[{*.md,*.rst}]
trim_trailing_whitespace = false

[{Makefile,*.mk,*.bat}]
indent_style = tab
indent_size = 2

[*.pem]
insert_final_newline = false

[*.py]
max_line_length = 119

[{*.cmake,CMakeLists.txt}]
indent_style = space
indent_size = 4
max_line_length = 120

[{*.sh,*.yml,*.yaml}]
indent_size = 2

[*.ini]
indent_size = 2

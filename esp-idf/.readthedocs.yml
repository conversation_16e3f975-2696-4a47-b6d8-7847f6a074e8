# .readthedocs.yml
# Read the Docs configuration file
# See https://docs.readthedocs.io/en/stable/config-file/v2.html for details

# Required
version: 2

# Optionally build your docs in additional formats such as PDF and ePub
formats:
  - pdf

# Optionally set the version of Python and requirements required to build your docs
python:
  version: 2.7
  install:
    - requirements: docs/requirements.txt

# We need to list all the submodules included in documenation build by Doxygen
submodules:
  include:
    - components/mqtt/esp-mqtt

workflow:
  rules:
    # Disable those non-protected push triggered pipelines
    - if: '$CI_COMMIT_REF_NAME != "master" && $CI_COMMIT_BRANCH !~ /^release\/v/ && $CI_COMMIT_TAG !~ /^v\d+\.\d+(\.\d+)?($|-)/ && $CI_COMMIT_TAG !~ /^qa-test/ && $CI_PIPELINE_SOURCE == "push"'
      when: never
    # when running merged result pipelines, CI_COMMIT_SHA represents the temp commit it created.
    # Please use PIPELINE_COMMIT_SHA at all places that require a commit sha of the original commit.
    - if: $CI_OPEN_MERGE_REQUESTS != null
      variables:
        PIPELINE_COMMIT_SHA: $CI_MERGE_REQUEST_SOURCE_BRANCH_SHA
        IS_MR_PIPELINE: 1
    - if: $CI_OPEN_MERGE_REQUESTS == null
      variables:
        PIPELINE_COMMIT_SHA: $CI_COMMIT_SHA
        IS_MR_PIPELINE: 0
    - when: always

# Place the default settings in `.gitlab/ci/common.yml` instead

include:
  - '.gitlab/ci/danger.yml'
  - '.gitlab/ci/common.yml'
  - '.gitlab/ci/rules.yml'
  - '.gitlab/ci/upload_cache.yml'
  - '.gitlab/ci/docs.yml'
  - '.gitlab/ci/static-code-analysis.yml'
  - '.gitlab/ci/pre_commit.yml'
  - '.gitlab/ci/pre_check.yml'
  - '.gitlab/ci/build.yml'
  - '.gitlab/ci/integration_test.yml'
  - '.gitlab/ci/host-test.yml'
  - '.gitlab/ci/deploy.yml'
  - '.gitlab/ci/test-win.yml'

# sdkconfig replacement configurations for deprecated options formatted as
# CONFIG_DEPRECATED_OPTION CONFIG_NEW_OPTION

# Compiler options
CONFIG_OPTIMIZATION_COMPILER                  CONFIG_COMPILER_OPTIMIZATION
CONFIG_OPTIMIZATION_LEVEL_DEBUG               CONFIG_COMPILER_OPTIMIZATION_DEBUG
CONFIG_OPTIMIZATION_LEVEL_RELEASE             CONFIG_COMPILER_OPTIMIZATION_SIZE
CONFIG_COMPILER_OPTIMIZATION_LEVEL_DEBUG      CONFIG_COMPILER_OPTIMIZATION_DEBUG
CONFIG_COMPILER_OPTIMIZATION_DEFAULT          CONFIG_COMPILER_OPTIMIZATION_DEBUG
CONFIG_COMPILER_OPTIMIZATION_LEVEL_RELEASE    CONFIG_COMPILER_OPTIMIZATION_SIZE
CONFIG_OPTIMIZATION_ASSERTION_LEVEL           CONFIG_COMPILER_OPTIMIZATION_ASSERTION_LEVEL
CONFIG_OPTIMIZATION_ASSERTIONS_ENABLED        CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE
CONFIG_OPTIMIZATION_ASSERTIONS_SILENT         CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_SILENT
CONFIG_OPTIMIZATION_ASSERTIONS_DISABLED       CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE
CONFIG_CXX_EXCEPTIONS                         CONFIG_COMPILER_CXX_EXCEPTIONS
CONFIG_CXX_EXCEPTIONS_EMG_POOL_SIZE           CONFIG_COMPILER_CXX_EXCEPTIONS_EMG_POOL_SIZE
CONFIG_STACK_CHECK_MODE                       CONFIG_COMPILER_STACK_CHECK_MODE
CONFIG_STACK_CHECK_NONE                       CONFIG_COMPILER_STACK_CHECK_MODE_NONE
CONFIG_STACK_CHECK_NORM                       CONFIG_COMPILER_STACK_CHECK_MODE_NORM
CONFIG_STACK_CHECK_STRONG                     CONFIG_COMPILER_STACK_CHECK_MODE_STRONG
CONFIG_STACK_CHECK_ALL                        CONFIG_COMPILER_STACK_CHECK_MODE_ALL
CONFIG_STACK_CHECK                            CONFIG_COMPILER_STACK_CHECK
CONFIG_WARN_WRITE_STRINGS                     CONFIG_COMPILER_WARN_WRITE_STRINGS
CONFIG_NO_BLOBS                               CONFIG_APP_NO_BLOBS
CONFIG_ESP32_COMPATIBLE_PRE_V2_1_BOOTLOADERS  CONFIG_APP_COMPATIBLE_PRE_V2_1_BOOTLOADERS
CONFIG_ESP32_COMPATIBLE_PRE_V3_1_BOOTLOADERS  CONFIG_APP_COMPATIBLE_PRE_V3_1_BOOTLOADERS
CONFIG_APP_BUILD_TYPE_ELF_RAM                 CONFIG_APP_BUILD_TYPE_RAM

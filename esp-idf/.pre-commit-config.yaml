# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks

default_stages: [commit]

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
        # note: whitespace exclusions use multiline regex, see https://pre-commit.com/#regular-expressions
        # items are:
        # 1 - some file extensions
        # 2 - any file matching *test*/*expected* (for host tests, if possible use this naming pattern always)
        # 3 - any directory named 'testdata'
        # 4 - protobuf auto-generated files
        exclude: &whitespace_excludes |
          (?x)^(
            .+\.(md|rst|map|bin)|
            .+test.*\/.*expected.*|
            .+\/testdata\/.+|
            .*_pb2.py|
            .*.pb-c.h|
            .*.pb-c.c|
            .*.yuv|
            .*.rgb
          )$
      - id: end-of-file-fixer
        exclude: *whitespace_excludes
      - id: check-executables-have-shebangs
      - id: mixed-line-ending
        args: ['-f=lf']
      - id: double-quote-string-fixer
      - id: no-commit-to-branch
        name: Do not use more than one slash in the branch name
        args: ['--pattern', '^[^/]*/[^/]*/']
      - id: no-commit-to-branch
        name: Do not use uppercase letters in the branch name
        args: ['--pattern', '^[^A-Z]*[A-Z]']
  - repo: https://github.com/PyCQA/flake8
    rev: 5.0.4
    hooks:
      - id: flake8
        args: ['--config=.flake8', '--tee', '--benchmark']
  - repo: https://github.com/asottile/reorder-python-imports
    rev: v3.12.0
    hooks:
      - id: reorder-python-imports
        name: Reorder Python imports
        args: [--py38-plus]
        exclude: >
          (?x)^(
              .*_pb2.py
          )$
  - repo: https://github.com/codespell-project/codespell
    rev: v2.2.6
    hooks:
      - id: codespell
  - repo: local
    hooks:
      - id: check-executables
        name: Check File Permissions
        entry: tools/ci/check_executables.py --action executables
        language: python
        types: [executable]
        exclude: '\.pre-commit/.+'
      - id: check-executable-list
        name: Validate executable-list.txt
        entry: tools/ci/check_executables.py --action list
        language: python
        pass_filenames: false
        always_run: true
      - id: check-deprecated-kconfigs-options
        name: Check if any Kconfig Options Deprecated
        entry: tools/ci/check_deprecated_kconfigs.py
        language: python
        files: 'sdkconfig\.ci$|sdkconfig\.rename$|sdkconfig.*$'
      - id: cmake-lint
        name: Check CMake Files Format
        entry: cmakelint --linelength=120 --spaces=4 --filter=-whitespace/indent
        language: python
        additional_dependencies:
          - cmakelint==1.4.1
        files: 'CMakeLists.txt$|\.cmake$'
        exclude: '\/third_party\/'
      - id: check-codeowners
        name: Validate Codeowner File
        entry: tools/ci/check_codeowners.py ci-check
        language: python
        always_run: true
        files: '\.gitlab/CODEOWNERS'
        pass_filenames: false
      - id: check-generated-rules
        name: Check rules are generated (based on .gitlab/ci/dependencies/dependencies.yml)
        entry: tools/ci/generate_rules.py
        language: python
        files: '\.gitlab/ci/dependencies/.+|\.gitlab/ci/.*\.yml|.gitlab-ci.yml'
        pass_filenames: false
        require_serial: true
        additional_dependencies:
          - PyYAML == 5.3.1
      - id: mypy-check
        name: Check type annotations in python files
        entry: tools/ci/check_type_comments.py
        additional_dependencies:
          - 'mypy'
          - 'mypy-extensions'
          - 'types-setuptools'
          - 'types-PyYAML'
          - 'types-requests'
        exclude: >
          (?x)^(
              .*_pb2.py
          )$
        language: python
        types: [python]
      - id: check-requirement-files
        name: Check requirement files
        entry: tools/ci/check_requirement_files.py
        additional_dependencies:
          - 'jsonschema'
        language: python
        files: 'tools/requirements.+|tools/requirements/.+'
        pass_filenames: false
      - id: check-tools-files-patterns
        name: Check tools dir files patterns
        entry: tools/ci/check_tools_files_patterns.py
        language: python
        files: '^tools/.+'
        additional_dependencies:
          - PyYAML == 5.3.1
        pass_filenames: false
      - id: check-rules-components-patterns
        name: check patterns-build_components in rules.yml
        entry: tools/ci/check_rules_components_patterns.py
        language: python
        files: 'components/.+|.gitlab/ci/rules.yml'
        additional_dependencies:
          - PyYAML == 5.3.1
        pass_filenames: false
      - id: check-generated-soc-caps-kconfig
        name: Check soc caps kconfig files are generated (based on components/soc/IDF_TARGET/include/soc/soc_caps.h)
        entry: tools/gen_soc_caps_kconfig/gen_soc_caps_kconfig.py -d 'components/soc/*/include/soc/' 'components/esp_rom/*/' 'components/spi_flash/*/'
        language: python
        files: 'components/soc/.+/include/soc/.+_caps\.h|components/esp_rom/.+/.+_caps\.h|kconfig\.soc_caps.in|components/spi_flash/.+/.+_caps\.h'
        pass_filenames: false
        additional_dependencies:
          - pyparsing
      - id: check-all-apps-readmes
        name: Check if all apps readme files match given .build-test-rules.yml files. Modify the supported target tables
        entry: tools/ci/check_build_test_rules.py check-readmes
        language: python
        files: 'tools/test_apps/.+|examples/.+|components/.+|tools/idf_py_actions/constants.py|tools/ci/check_build_test_rules.py'
        require_serial: true
        additional_dependencies:
          - PyYAML == 5.3.1
          - idf-build-apps~=2.0
      - id: sort-yaml-files
        name: sort yaml files
        entry: tools/ci/sort_yaml.py
        language: python
        files: '\.build-test-rules\.yml$|known_generate_test_child_pipeline_warnings\.yml$'
        additional_dependencies:
          - ruamel.yaml
      - id: sort-yaml-test
        name: sort yaml test
        entry: python -m unittest tools/ci/sort_yaml.py
        language: python
        files: 'tools/ci/sort_yaml\.py$'
        additional_dependencies:
          - ruamel.yaml
      - id: check-build-test-rules-path-exists
        name: check path in .build-test-rules.yml exists
        entry: tools/ci/check_build_test_rules.py check-exist
        language: python
        additional_dependencies:
          - PyYAML == 5.3.1
        always_run: true
        pass_filenames: false
        require_serial: true
      - id: cleanup-ignore-lists
        name: Remove non-existing patterns from ignore lists
        entry: tools/ci/cleanup_ignore_lists.py
        language: python
        always_run: true
        require_serial: true
      - id: gitlab-yaml-linter
        name: Check gitlab yaml files
        entry: tools/ci/gitlab_yaml_linter.py
        language: python
        files: '\.gitlab-ci\.yml|\.gitlab/ci/.+\.yml|\.gitmodules'
        pass_filenames: false
        additional_dependencies:
          - PyYAML == 5.3.1
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.0.1
    hooks:
      - id: file-contents-sorter
        files: 'tools\/ci\/(executable-list\.txt|mypy_ignore_list\.txt|check_copyright_ignore\.txt)'
  - repo: https://github.com/espressif/check-copyright/
    rev: v1.0.3
    hooks:
      - id: check-copyright
        args: ['--ignore', 'tools/ci/check_copyright_ignore.txt', '--config', 'tools/ci/check_copyright_config.yaml']
  - repo: https://github.com/espressif/conventional-precommit-linter
    rev: v1.7.0
    hooks:
      - id: conventional-precommit-linter
        stages: [commit-msg]
  - repo: https://github.com/espressif/astyle_py.git
    rev: v1.0.5
    hooks:
      - id: astyle_py
        # If you are modifying astyle version, update tools/format.sh as well
        args: ['--astyle-version=3.4.7', '--rules=tools/ci/astyle-rules.yml']
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.9.0.5
    hooks:
    - id: shellcheck
      name: shellcheck bash
      args: ['--shell', 'bash', '-x']
      files: 'install.sh|export.sh'
    - id: shellcheck
      name: shellcheck dash (export.sh)
      args: ['--shell', 'dash', '-x']
      files: 'export.sh'
  - repo: https://github.com/espressif/esp-idf-sbom.git
    rev: v0.13.0
    hooks:
      - id: validate-sbom-manifest
        stages: [post-commit]
  - repo: https://github.com/sphinx-contrib/sphinx-lint
    rev: v0.9.1
    hooks:
      - id: 'sphinx-lint'
        name: Lint rST files in docs folder using Sphinx Lint
        files: ^(docs/en|docs/zh_CN)/.*\.(rst|inc)$
  - repo: https://github.com/espressif/esp-idf-kconfig.git
    rev: v2.1.0
    hooks:
      - id: check-kconfig-files

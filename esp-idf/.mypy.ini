[mypy]

# Specifies the Python version used to parse and check the target program
python_version = 3.9

# Disallows defining functions without type annotations or with incomplete type annotations
# True => enforce type annotation in all function definitions
disallow_untyped_defs = True

# Shows a warning when returning a value with type Any from a function declared with a non- Any return type
warn_return_any = True

# Shows errors for missing return statements on some execution paths
warn_no_return = True

# Suppress error messages about imports that cannot be resolved
# True => ignore all import errors
ignore_missing_imports = True

# Disallows defining functions with incomplete type annotations
disallow_incomplete_defs = False

# Directs what to do with imports when the imported module is found as a .py file and not part of the files,
# modules and packages provided on the command line.
# SKIP -> mypy checks only single file, not included imports
follow_imports = skip

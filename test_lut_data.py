#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的LUT方法，创建测试数据并验证功能
"""

import pandas as pd
import numpy as np
import os

def create_test_excel():
    """创建测试用的Excel文件，格式与calc_sensor_uv_map.py一致"""
    
    # 创建测试数据
    # 实际电流A：从0到30A
    actual_current = np.linspace(0, 30, 31)
    
    # 实际微伏uV：理想情况下应该是线性关系，但加入一些非线性误差
    # 基础线性关系：uV = 5000 * I
    base_uv = 5000 * actual_current
    
    # 添加一些非线性误差（模拟传感器的非线性特性）
    nonlinear_error = 200 * np.sin(actual_current * 0.3) + 100 * (actual_current / 30) ** 2
    actual_uv = base_uv + nonlinear_error
    
    # 创建DataFrame
    df = pd.DataFrame({
        '实际电流A': actual_current,
        '实际微伏uV': actual_uv
    })
    
    # 保存到Excel文件
    test_file = 'test_sensor_data.xlsx'
    df.to_excel(test_file, index=False)
    print(f"测试Excel文件已创建: {test_file}")
    print(f"数据范围: 电流 {actual_current.min():.1f}A - {actual_current.max():.1f}A")
    print(f"微伏范围: {actual_uv.min():.0f}µV - {actual_uv.max():.0f}µV")
    
    return test_file

def test_lut_processing():
    """测试LUT处理功能"""
    try:
        # 创建测试Excel文件
        test_file = create_test_excel()
        
        # 导入calc_sensor_uv_map.py的函数
        from calc_sensor_uv_map import (
            load_two_cols_xlsx, build_targets, piecewise_linear_lut_n,
            I_COL, UV_COL
        )
        
        print("\n=== 测试LUT处理功能 ===")
        
        # 加载测试数据
        test_df = load_two_cols_xlsx(test_file)
        main_df = test_df.copy()  # 使用相同数据作为参考
        
        print(f"加载数据成功，共{len(test_df)}行")
        
        # 构建目标微伏
        target_uv = build_targets(main_df, test_df)
        
        # 构造LUT
        uv_raw = test_df[UV_COL].astype(float).to_numpy()
        n_knots = 15
        
        xi, yi = piecewise_linear_lut_n(uv_raw, target_uv, n_knots)
        
        # 转换为整数
        xi_i = [int(round(v)) for v in xi]
        yi_i = [int(round(v)) for v in yi]
        
        print(f"LUT生成成功，节点数: {len(xi_i)}")
        print(f"LUT_X范围: {min(xi_i)} - {max(xi_i)}")
        print(f"LUT_Y范围: {min(yi_i)} - {max(yi_i)}")
        print(f"LUT_X前5个点: {xi_i[:5]}")
        print(f"LUT_Y前5个点: {yi_i[:5]}")
        
        # 测试插值
        uv_corrected = np.interp(uv_raw, xi, yi)
        
        # 计算误差
        error_before = np.abs(uv_raw - target_uv)
        error_after = np.abs(uv_corrected - target_uv)
        
        print(f"\n校正效果:")
        print(f"校正前平均误差: {error_before.mean():.1f} µV")
        print(f"校正后平均误差: {error_after.mean():.1f} µV")
        print(f"校正前最大误差: {error_before.max():.1f} µV")
        print(f"校正后最大误差: {error_after.max():.1f} µV")
        
        return xi_i, yi_i, len(xi_i)
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_firmware_data_format():
    """测试固件数据格式"""
    result = test_lut_processing()
    if result is None:
        return
        
    lut_x, lut_y, lut_size = result
    
    print(f"\n=== 固件数据格式测试 ===")
    
    # 模拟固件数据写入格式
    import struct
    
    lut_size_offset = 0x3080
    lut_x_offset = 0x3084
    lut_y_offset = lut_x_offset + lut_size * 4
    
    print(f"LUT大小存储位置: 0x{lut_size_offset:04X}")
    print(f"LUT_X数组位置: 0x{lut_x_offset:04X} - 0x{lut_x_offset + lut_size*4 - 1:04X}")
    print(f"LUT_Y数组位置: 0x{lut_y_offset:04X} - 0x{lut_y_offset + lut_size*4 - 1:04X}")
    print(f"总占用空间: {lut_y_offset + lut_size*4 - lut_size_offset} 字节")
    
    # 打包数据
    lut_size_data = struct.pack('<I', lut_size)
    print(f"LUT大小数据: {lut_size_data.hex()}")
    
    lut_x_data = b''.join(struct.pack('<I', int(x)) for x in lut_x)
    print(f"LUT_X数据前16字节: {lut_x_data[:16].hex()}")
    
    lut_y_data = b''.join(struct.pack('<I', int(y)) for y in lut_y)
    print(f"LUT_Y数据前16字节: {lut_y_data[:16].hex()}")
    
    print("\n传感器读取示例代码:")
    print("""
// C代码示例
uint32_t lut_size = *(uint32_t*)0x3080;
uint32_t* lut_x = (uint32_t*)0x3084;
uint32_t* lut_y = (uint32_t*)(0x3084 + lut_size * 4);

uint32_t correct_uv(uint32_t raw_uv) {
    for (int i = 0; i < lut_size - 1; i++) {
        if (raw_uv >= lut_x[i] && raw_uv <= lut_x[i+1]) {
            uint32_t x0 = lut_x[i], x1 = lut_x[i+1];
            uint32_t y0 = lut_y[i], y1 = lut_y[i+1];
            return y0 + (y1 - y0) * (raw_uv - x0) / (x1 - x0);
        }
    }
    return raw_uv;
}
""")

if __name__ == "__main__":
    print("=== 新LUT方法测试 ===")
    test_firmware_data_format()
    print("\n测试完成！")

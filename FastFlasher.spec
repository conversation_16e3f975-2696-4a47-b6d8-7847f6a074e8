# -*- mode: python ; coding: utf-8 -*-

import os
import esptool # For dynamically finding esptool path

block_cipher = None

# Determine the base directory of the spec file (project root)
spec_file_dir = os.getcwd()

# Dynamically locate esptool and prepare its data files
esptool_package_path = os.path.dirname(esptool.__file__)
esptool_datas = [
    (os.path.join(esptool_package_path, 'targets/stub_flasher/1/esp32s3.json'), 'esptool/targets/stub_flasher/1'),
    (os.path.join(esptool_package_path, 'targets/stub_flasher/2/esp32s3.json'), 'esptool/targets/stub_flasher/2'),
    # Example: If you need the entire esp32s3 target data directory:
    # (os.path.join(esptool_package_path, 'targets', 'esp32s3'), os.path.join('esptool', 'targets', 'esp32s3')),
]

# Application-specific data
app_specific_datas = [
    ('certificate', 'certificate'),
    ('hyplink', 'hyplink'),
    ('hypsensor', 'hypsensor'),
    ('configure_esp_secure_cert.py', '.'),
    ('esp_secure_cert', 'esp_secure_cert'), # User should verify if this is data or a Python package
    ('esp-idf', 'esp-idf'),
]

# Combine all datas
combined_datas = app_specific_datas + esptool_datas


a = Analysis(
    ['FastFlasher.py'],
    pathex=[spec_file_dir], # Explicitly set pathex to where the spec file is
    binaries=[], # PyInstaller typically auto-detects necessary binaries
    datas=combined_datas, # Use the combined and dynamically generated list
    hiddenimports=[
        'serial.tools.list_ports', 
        'pkg_resources.py2_warn', # Common hidden import for some packages
        'cryptography',            # For esp_secure_cert.py and esptool
        'cryptography.hazmat.primitives.serialization',
        'cryptography.hazmat.backends',
        'cryptography.x509',
        'esptool',                 # If esptool is imported or run via runpy
        'esptool.__main__',        # For `python -m esptool` or runpy.run_module('esptool')
        # 'json' is a standard library module, usually not needed here.
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=1, # Changed from 0 for bytecode optimization
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [], 
    exclude_binaries=True, 
    name='FastFlasher',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False, # Set to False for a GUI application (no background console window)
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    # icon=os.path.join(spec_file_dir, 'your_icon.ico') # Example: Uncomment and provide path to your icon
)

# This COLLECT block is typical for a one-folder bundle (--onedir)
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas, # Ensure a.datas from Analysis is passed here
    strip=False,
    upx=True, # Match EXE's UPX setting
    upx_exclude=[],
    name='FastFlasher' # This will be the name of the folder in dist/
)

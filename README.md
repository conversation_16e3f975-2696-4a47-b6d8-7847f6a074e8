# FastFlasher 量产烧录工具文档

本页面介绍 Beima-Tech 自研的 FastFlasher 烧录工具，用于传感器与网关设备的量产编程。该工具图形化操作，支持传感器ID/网关ID写入与多阶段烧录任务，适配 CC2652R7 和 ESP32-S3 的设备。它还集成了ID生成和QR码生成功能。

---

## 目录结构

这个目录结构展示了开发者在打包前项目源文件的典型布局。当使用 PyInstaller 通过 `FastFlasher.spec` 打包后 (通常是 `--onedir` 文件夹模式，会在 `dist/` 目录下生成一个如 `FastFlasher/` 的文件夹)，所有在 `.spec` 文件中 `datas` 指定的文件和文件夹（如 `hypsensor/`, `hyplink/`, `certificate/`, `configure_esp_secure_cert.py`, `esp_secure_cert/`, `esp-idf/`）都会被复制到这个输出文件夹内，与主程序 `FastFlasher.exe` 在同一结构层级或指定的子目录中。

```
FastFlasher_Project_Root/
├── FastFlasher.py              # Python 源代码主文件
├── FastFlasher.spec            # PyInstaller 配置文件
├── README.md                   # 本文档
#原始的 sensor_ids.txt 和 gateway_ids.txt 现在由用户通过UI选择或由ID生成器创建，不再是项目预置文件。
#用户可以在任何位置准备这些ID文件，并通过程序的浏览按钮指定它们。
├── configure_esp_secure_cert.py  # ESP32安全证书配置脚本 (源文件，与FastFlasher.py同级)
├── esp_secure_cert/            # `configure_esp_secure_cert.py` 依赖的Python包 (源文件夹，与FastFlasher.py同级)
│   ├── ... (包内部结构)
├── esp-idf/                    # ESP-IDF 相关资源，如果打包脚本需要
├── hypsensor/                  # 传感器烧录工具与固件目录
│   ├── hyp60.exe               # 传感器应用固件（普通版本）
│   ├── hyp60-bc.exe            # 传感器应用固件（广播版本）
│   ├── xxx-mcuboot.exe         # TI 烧录工具（例如：cc2652r7-mcuboot.exe）
│   ├── xxx-mcuboot.bin         # 对应的 .bin 文件，用于写入传感器ID和标定系数
│   └── config.ini              # 传感器烧录配置（由主程序自动同步更新打包内的版本）
├── CalibrationForm/            # 标定数据导出目录（程序运行时自动创建）
│   └── [传感器ID]_calibrated.xlsx  # 导出的校正后标定数据
├── hyplink/                    # 网关烧录固件目录
│   ├── bootloader.bin
│   ├── hyplink.bin
│   ├── partition-table.bin
│   ├── ota_data_initial.bin
│   ├── rcp_fw.bin
│   └── spiffs_storage.bin      # 将写入网关ID的SPIFFS镜像
├── certificate/                # AWS 证书目录
│   ├── AmazonRootCA1.pem
│   ├── device_cert.pem.crt
│   └── device_private.pem.key
└── dist/                       # PyInstaller 打包输出目录 (示例)
    └── FastFlasher/            # 打包后的应用程序文件夹 (如果使用 --onedir)
        ├── FastFlasher.exe     # 主可执行文件
        # sensor_ids.txt 和 gateway_ids.txt 不再默认随包分发。
        # 用户通过程序UI选择ID文件，或使用内置ID生成器创建。
        ├── configure_esp_secure_cert.py # 被 spec 文件复制进来
        ├── esp_secure_cert/    # 被 spec 文件复制进来
        ├── esp-idf/            # 被 spec 文件复制进来
        ├── hypsensor/          # 被 spec 文件复制进来
        ├── hyplink/            # 被 spec 文件复制进来
        ├── certificate/        # 被 spec 文件复制进来
        └── ... (其他Python依赖库和DLLs)
```

**对于最终用户 (使用 `--onedir` 文件夹模式打包)：**
用户会得到一个名为 `FastFlasher` (或其他在 `.spec` 中指定名称) 的文件夹。需要将整个文件夹分发给用户。用户通过程序界面选择包含设备ID的 `.txt` 文件，或使用内置的 "ID 生成" 功能创建这些文件。

---

## 快速使用指南

### 一、准备工作

1.  **获取程序**: 从发布渠道获取包含 `FastFlasher.exe` 及其所有依赖项的 `FastFlasher` 文件夹。
2.  **准备ID文件 (可选，但推荐)**:
    *   用户可以预先准备包含设备ID的文本文件 (`.txt`)。每个ID占一行，格式为8位字母数字。
    *   这些文件可以存放在计算机的任何位置。在烧录或QR码生成时，可以通过程序界面上的 "..." 或 "选择文件" 按钮来指定这些文件。
    *   或者，可以使用程序内置的 "ID 生成" 功能来创建新的ID列表文件。
3.  **(开发阶段)**: 开发者需确保 `FastFlasher.py`、`FastFlasher.spec`、以及所有在 `.spec` 中 `datas` 部分引用的源文件和文件夹（如 `configure_esp_secure_cert.py`, `esp_secure_cert/`, `hypsensor/`, `hyplink/`, `certificate/`, `esp-idf/`）在项目中具有正确的相对路径关系，以便 `FastFlasher.spec` 文件能找到它们并将它们打包。

### 二、运行程序

进入 `FastFlasher` 文件夹，双击运行 `FastFlasher.exe`。界面现在包含多个功能标签页。

> ![image](https://github.com/user-attachments/assets/3e145168-9e33-43bc-80d4-6f4c34a57485)
> ![image](https://github.com/user-attachments/assets/db0fdd81-adaf-49f5-b01c-44cc46ac986f)
> (界面截图可能需要更新以反映新的选项卡如ID生成和QR码生成)


工具界面包含主要标签页：**传感器 (Sensor)**, **传感器标定**, **网关 (Gateway)**, **ID 生成**, 和 **QR码生成**。

---

## 三、传感器烧录流程

### 3.1 传感器 (Sensor) 标签页 - 简化烧录

1.  切换到"**传感器 (Sensor)**"标签页。
2.  **串口选择**: 从下拉列表中选择连接到传感器的COM端口。列表会显示端口号和设备描述。每次点击下拉框会刷新端口列表。
3.  **波特率**: 选择烧录所需的波特率（建议230400）。
4.  **使用广播版本**: 可选择是否使用广播版本固件（hyp60-bc.exe）。
5.  点击"**开始传感器烧录**"按钮。烧录过程包括：
    *   **直接固件烧录**: 跳过MCUBoot和ID写入步骤，直接执行传感器固件烧录（hyp60.exe 或 hyp60-bc.exe）。
    *   所有操作的详细日志会实时显示在下方的文本区域。

> **注意**: 此标签页适用于已经预烧录了MCUBoot和ID的传感器，或仅需要更新应用固件的场景。

### 3.2 传感器标定 标签页 - 完整烧录与标定

1.  切换到"**传感器标定**"标签页。
2.  **串口选择**: 从下拉列表中选择连接到传感器的COM端口。
3.  **波特率**: 选择烧录所需的波特率（建议230400）。
4.  **MCUBoot工具**: 工具会自动扫描程序内部 `hypsensor/` 目录，并列出 `*-mcuboot.exe` 工具。选择一个。
5.  **传感器ID**:
    *   点击ID输入框旁的 "..." 按钮，浏览并选择一个包含传感器ID的 `.txt` 文件。程序会加载该文件中的ID列表。
    *   使用ID输入框旁的 `<` (上一个) 和 `>` (下一个) 按钮可以在已加载的ID之间切换。
    *   也可以直接在ID输入框中手动输入一个有效的8位字母数字ID。
6.  **序号**: 可以直接跳转到指定序号的ID。
7.  **标定数据Excel**: **必须选择**包含标定数据的Excel文件。Excel文件必须包含以下三列：
    *   `实际微伏uV`: 实际测量的微伏值
    *   `实际电流A`: 实际电流值
    *   `读出电流A` (或 `读out电流A`, `readA`): 传感器读出的电流值
8.  **使用广播版本**: 可选择是否使用广播版本固件。
9.  点击"**开始传感器烧录**"按钮。烧录过程包括：
    *   **Excel数据验证**: 验证Excel文件格式和数据有效性。
    *   **标定系数计算**: 根据Excel数据计算三个标定系数：
        - `uV_threshold`: 阈值（微伏）
        - `gain`: 增益系数
        - `offset`: 偏移量
    *   **ID写入**: 将传感器ID写入到MCUBoot工具对应的 `.bin` 文件的0x3070位置。
    *   **标定系数写入**: 将计算出的三个系数写入到 `.bin` 文件的0x3080位置。
    *   **校正数据导出**: 自动导出校正后的Excel文件到 `CalibrationForm/传感器ID_calibrated.xlsx`。
    *   **Bootloader烧录**: 执行选定的 `*-mcuboot.exe` 工具。
    *   **应用固件烧录**: 执行 `hyp60.exe` 或 `hyp60-bc.exe`。
    *   所有操作的详细日志会实时显示在下方的文本区域。

> **注意**: 传感器标定功能需要安装pandas和numpy库。导出的校正后Excel文件包含原始数据以及校正电流、校正后绝对误差和校正后误差百分比等分析数据。

---

---

## 四、传感器标定功能详解

传感器标定功能是本工具的核心特性之一，用于根据实际测试数据计算传感器的标定系数，并将这些系数烧录到传感器固件中。

### 4.1 标定原理

传感器标定基于线性校正模型，通过分析实际测量数据计算出三个关键参数：

1. **uV_threshold（阈值）**: 微伏阈值，用于判断是否需要进行线性校正
2. **gain（增益）**: 线性校正的增益系数
3. **offset（偏移）**: 线性校正的偏移量

校正公式：
```
当 实际微伏uV > uV_threshold 时：
校正电流A = gain × 读出电流A + offset

否则：
校正电流A = 读出电流A
```

### 4.2 Excel数据格式要求

标定数据Excel文件必须包含以下三列（列名必须精确匹配）：

| 列名 | 说明 | 示例值 |
|------|------|--------|
| `实际微伏uV` | 实际测量的微伏值 | 125000 |
| `实际电流A` | 标准电流值（真实值） | 20.5 |
| `读出电流A` | 传感器读出的电流值 | 19.8 |

> **注意**: 也支持 `读out电流A` 或 `readA` 作为读出电流列名的替代。

### 4.3 标定系数计算方法

1. **阈值计算**:
   - 对所有数据进行线性拟合：`实际微伏uV = k × 实际电流A + b`
   - 计算20A处的微伏值：`uV_at_20A = k × 20 + b`
   - 向上取整到5000的倍数作为阈值

2. **增益和偏移计算**:
   - 对所有数据进行线性回归：`实际电流A = gain × 读出电流A + offset`
   - 得到gain和offset参数

### 4.4 数据写入位置

计算出的标定系数会写入到MCUBoot固件的特定位置：

- **传感器ID**: 写入0x3070位置（8字节）
- **标定系数**: 写入0x3080位置（12字节）
  - uV_threshold: uint32_t（4字节）
  - gain: float（4字节）
  - offset: float（4字节）

### 4.5 校正数据导出

标定完成后，程序会自动导出包含校正分析的Excel文件：

- **文件位置**: `CalibrationForm/传感器ID_calibrated.xlsx`
- **新增列**:
  - `校正电流A`: 根据标定系数计算的校正电流值
  - `校正后绝对误差A`: |校正电流A - 实际电流A|
  - `校正后误差百分比`: (校正后绝对误差A / 实际电流A) × 100
- **统计信息**: 显示平均绝对误差和最大绝对误差

---

## 五、网关烧录流程

1.  切换到"**网关 (Gateway)**"标签页。
1.  选择串口（COM）。
2.  通过 "..." 按钮选择包含网关ID的 `.txt` 文件，或手动输入ID。
3.  **网关ID**:
    *   点击ID输入框旁的 "..." 按钮，浏览并选择一个包含网关ID的 `.txt` 文件。程序会加载该文件中的ID列表。上次成功选择的文件路径会被保存并在下次启动时尝试自动加载。
    *   使用ID输入框旁的 `<` 和 `>` 按钮可以在已加载的ID之间切换。
    *   也可以直接在ID输入框中手动输入一个有效的8位字母数字ID。
4.  点击"**开始网关烧录**"按钮。烧录过程包括多个步骤：
    *   **证书烧录**: 调用 `configure_esp_secure_cert.py` 脚本烧录证书。
    *   **ID写入SPIFFS**: 将当前选定的网关ID写入到 `spiffs_storage.bin` 文件。
    *   **固件烧录**: 使用 `esptool` 烧录完整的固件镜像。
    *   所有操作的详细日志会实时显示在下方的文本区域。

---

## 六、ID 生成功能

此功能位于 "**ID 生成**" 标签页，用于为不同设备型号批量生成唯一的8位ID。

1.  **选择型号**: 从下拉菜单中选择设备型号（如 网关, hyp60, hyp225, hypx）。
2.  **生成数量**: 输入希望生成的ID数量。
3.  **指定日期 (可选)**: 输入一个日期 (格式 YYYY-MM-DD)。如果留空，则默认使用当前日期。日期信息会编码到生成的ID中。
4.  点击 "**生成ID**" 按钮。
5.  **输出**:
    *   生成的ID会写入到一个以设备型号命名的 `.txt` 文件中 (例如 `gateway_ids.txt`, `hyp60_ids.txt`, `hypx_ids.txt` 等)。
    *   这些文件默认保存在程序资源目录（开发时）或打包应用的可执行文件所在的目录附近（具体路径见状态标签提示）。
    *   这些生成的ID文件可以被烧录流程中的文件选择功能或QR码生成功能使用。
    *   状态标签会显示生成结果和文件保存路径。

---

## 七、QR码生成功能

此功能位于 "**QR码生成**" 标签页，用于根据ID列表生成包含特定编码ID的QR码图片。

### 单个ID文件处理

1.  **ID 文件 (.txt)**: 点击 "**选择文件**" 按钮，选择一个包含原始ID的 `.txt` 文件 (每行一个ID)。
2.  **ID 类型**: 从下拉菜单中选择ID对应的类型 (HYP60, HYP225, HYPX, HYPLINK)。
    *   如果选择的ID文件名包含特定关键字 (如 "hyp60", "hypx" 等)，程序会自动尝试匹配并选择对应的ID类型。
3.  **保存目录**: 点击 "**选择目录**" 按钮，选择QR码图片的根保存位置。
    *   默认情况下，当选择ID文件后，此保存目录会自动设置为该ID文件所在的目录。
4.  点击 "**开始生成QR码**" 按钮。

### 批量ID文件处理

1.  点击 "**批量生成 (选择目录)**" 按钮。
2.  在弹出的对话框中，选择一个包含多个ID `.txt` 文件的根目录。
3.  程序会自动扫描该目录及其子目录（不包括名为 "QR" 的子目录）下的所有 `.txt` 文件。
4.  对于每个找到的 `.txt` 文件，程序会尝试根据文件名推断其ID类型。如果无法推断或类型无效，该文件将被跳过（控制台会输出警告）。
5.  QR码将基于此根目录进行保存，结构如下所述。

### 通用逻辑与输出

*   **QR码内容**: 每个原始ID会经过特定的Base36编码算法处理，并添加 `DP:` 前缀，形成最终编码到QR码中的字符串。
*   **QR码外观**: 生成的 `.png` 图片不仅包含QR码，在其下方还会显示原始的8位ID文本。
*   **文件命名**: 每张QR码图片会以其对应的原始ID（经过特殊字符清理后）命名，例如 `HKEA04ZB.png`。
*   **保存位置**:
    *   对于**单个文件处理**，QR码图片保存在：`所选的保存目录/QR/<ID类型>/`。
    *   对于**批量处理**，QR码图片保存在：`用户选择的根目录/QR/<推断的ID类型>/`。
    *   程序会自动创建这些子文件夹。
*   **旧文件清除**: 在为一批新的ID (无论是来自单个文件还是批量处理中的某个文件) 生成QR码之前，其对应的目标类型特定文件夹 (`.../QR/<ID类型>/`) 中所有已有的 `.png` 文件会被自动删除。
*   **状态反馈**: 界面底部的状态标签会显示处理进度、成功数量、失败数量以及最终保存路径等信息。

---

## 八、主要功能与注意事项

*   **ID格式**: 所有ID（传感器、网关、以及用于QR码生成的ID）都必须是8位长度，由字母和数字组成。
*   **串口独占**: 烧录前请确保所选串口未被其他任何程序占用。
*   **自动ID跳转**: 当从ID文件加载了ID列表后，每次成功烧录一个设备，工具会自动在UI上跳转到列表中的下一个ID。
*   **重复烧录确认**: 如果尝试烧录的ID与上一次在该类别成功烧录的ID相同，程序会弹出对话框要求确认。
*   **日志查看与清除**: 每个标签页下方的文本区域会显示详细的操作日志。可以用鼠标右键点击文本区域，选择"清空显示"来清除日志。
*   **配置文件 (`config.ini`)**:
    *   保存在与 `FastFlasher.exe` 同目录下。
    *   保存用户上次选择的COM口、波特率、MCUBoot工具、以及传感器和网关选项卡中**上次选择的ID文件路径**和**该文件内当前显示的ID**。
    *   传感器烧录相关的COM口、波特率和MCUBoot工具选择也会自动同步到程序内部 `hypsensor/config.ini` 文件。
*   **错误处理**: 程序会对一些已知的烧录错误进行捕获并给出特定提示。其他未预期错误也会在日志区显示详细信息。
*   **生成的文件**:
    *   由 "ID 生成" 功能创建的ID列表文件会保存在程序工作目录附近（具体路径见状态提示）。
    *   QR码图片会根据上述规则保存在指定的 `QR/<ID_TYPE>` 子文件夹中。
    *   传感器标定功能导出的校正后Excel文件会保存在 `CalibrationForm/` 目录中。
*   **依赖库要求**:
    *   传感器标定功能需要 `pandas` 和 `numpy` 库来处理Excel文件和进行数值计算。
    *   如果缺少这些库，程序会在尝试处理Excel文件时给出相应提示。

---

## 九、打包与部署

该工具使用 PyInstaller 进行打包，配置文件为 `FastFlasher.spec`。
执行打包命令（在项目根目录）：
```bash
pyinstaller FastFlasher.spec
```
这会依据 `.spec` 文件在 `dist/` 目录下生成一个名为 `FastFlasher` 的文件夹。这个文件夹包含了 `FastFlasher.exe` 以及所有运行所需的依赖和数据文件。

**部署 (`--onedir` 文件夹模式):**
将整个生成的 `dist/FastFlasher/` 文件夹分发给用户。用户不再需要在该文件夹内手动放置预置的 `sensor_ids.txt` 或 `gateway_ids.txt` 文件，而是通过程序UI的文件选择功能来指定这些ID文件的位置，或者使用内置的ID生成器创建它们。

(如果需要生成单文件 `--onefile` 版本，需要相应修改 `.spec` 文件或 PyInstaller 命令，并注意单文件启动时解压资源的性能开销。)

该工具打包后可在目标 Windows 机器上运行，无需预先安装 Python 环境。

---

## 十、支持平台

*   **操作系统**: Windows 10 / Windows 11
*   **目标芯片**:
    *   传感器: TI CC2652R7 (或兼容此烧录流程的其他型号)
    *   网关: ESP32-S3
*   **烧录接口**: USB 转串口模块 (如CH340等) / 设备自带的USB下载接口

---

## 十一、问题反馈

如果在工具使用过程中遇到任何问题或有改进建议，请联系 Beima-Tech 开发团队，或在公司内部的 Git 项目仓库中提交 Issue。

